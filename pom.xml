<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sinoyd.parse.webbackend</groupId>
    <artifactId>parseWebbackend</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>parseWebbackend</name>
    <description>框架启动项</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <tomcat.version>8.5.65</tomcat.version>
    </properties>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.15.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.sinoyd.parse</groupId>
            <artifactId>sinoyd-parse-impl</artifactId>
            <version>0.0.1Cloud-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.sinoyd.parse</groupId>
            <artifactId>sinoyd-parse-grpc</artifactId>
            <version>0.0.1Cloud-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <layout>ZIP</layout>
                    <!-- 设置需要包含在jar中的依赖jar-->
                    <includes>
                        <include>
                            <groupId>com.sinoyd.frame</groupId>
                            <artifactId>frame-arch</artifactId>
                        </include>
                        <include>
                            <groupId>com.sinoyd.frame</groupId>
                            <artifactId>frame-core</artifactId>
                        </include>
                        <include>
                            <groupId>com.sinoyd.parse</groupId>
                            <artifactId>sinoyd-parse-impl</artifactId>
                        </include>
                        <include>
                            <groupId>com.sinoyd.parse</groupId>
                            <artifactId>sinoyd-parse-public</artifactId>
                        </include>
                        <include>
                            <groupId>com.sinoyd.parse</groupId>
                            <artifactId>sinoyd-parse-grpc</artifactId>
                        </include>
                        <include>
                            <groupId>com.sinoyd.parse</groupId>
                            <artifactId>sinoyd-parse-arch</artifactId>
                        </include>
                        <include>
                            <groupId>com.sinoydcloud.boot</groupId>
                            <artifactId>sinoyd-boot-starter-common</artifactId>
                        </include>
                        <include>
                            <groupId>com.sinoydcloud.boot</groupId>
                            <artifactId>sinoyd-boot-starter-frame</artifactId>
                        </include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <!-- 设置需要排除在输出目录文件中的jar-->
                    <excludeArtifactIds>
                        frame-arch,frame-core,
                        sinoyd-parse-arch,
                        sinoyd-parse-impl,
                        sinoyd-parse-public,
                        sinoyd-parse-grpc,
                        sinoyd-boot-starter-common,
                        sinoyd-boot-starter-frame,
                        sinoyd-boot-starter-workflow-activiti
                    </excludeArtifactIds>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id><!--这里需要和settings.xml中配置的私有库名称一致-->
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>
</project>