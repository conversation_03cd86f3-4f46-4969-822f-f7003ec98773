package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.AiParseFileAppCriteria;
import com.sinoyd.parse.dto.DtoAiParseFileApp;
import com.sinoyd.parse.service.AiParseFileAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * AI仪器解析应用服务接口定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Validated
@Api(tags = "AI仪器解析应用服务")
@RestController
@RequestMapping("api/parse/aiParseFileApp")
public class AiParseFileAppController extends BaseJpaController<DtoAiParseFileApp, String, AiParseFileAppService> {

    /**
     * 分页动态条件查询AI仪器解析应用
     *
     * @param aiParseFileAppCriteria 条件参数
     * @return RestResponse<List<DtoAiParseFileApp>>
     */
    @ApiOperation(value = "分页动态条件查询AI仪器解析应用", notes = "分页动态条件查询AI仪器解析应用")
    @GetMapping
    public RestResponse<List<DtoAiParseFileApp>> findByPage(AiParseFileAppCriteria aiParseFileAppCriteria) {
        PageBean<DtoAiParseFileApp> pageBean = super.getPageBean();
        RestResponse<List<DtoAiParseFileApp>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, aiParseFileAppCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增AI仪器解析应用
     *
     * @param aiParseFileApp AI仪器解析应用实体
     * @return RestResponse<DtoAiParseFileApp>
     */
    @ApiOperation(value = "新增AI仪器解析应用", notes = "新增AI仪器解析应用")
    @PostMapping
    public RestResponse<DtoAiParseFileApp> save(@Valid @RequestBody DtoAiParseFileApp aiParseFileApp) {
        RestResponse<DtoAiParseFileApp> restResponse = new RestResponse<>();
        DtoAiParseFileApp result = service.save(aiParseFileApp);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * 修改AI仪器解析应用
     *
     * @param aiParseFileApp AI仪器解析应用实体
     * @return RestResponse<DtoAiParseFileApp>
     */
    @ApiOperation(value = "修改AI仪器解析应用", notes = "修改AI仪器解析应用")
    @PutMapping
    public RestResponse<DtoAiParseFileApp> update(@Valid @RequestBody DtoAiParseFileApp aiParseFileApp) {
        RestResponse<DtoAiParseFileApp> restResponse = new RestResponse<>();
        DtoAiParseFileApp result = service.update(aiParseFileApp);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * 根据ID查询AI仪器解析应用
     *
     * @param id 应用ID
     * @return RestResponse<DtoAiParseFileApp>
     */
    @ApiOperation(value = "根据ID查询AI仪器解析应用", notes = "根据ID查询AI仪器解析应用")
    @GetMapping("/{id}")
    public RestResponse<DtoAiParseFileApp> findById(@PathVariable String id) {
        RestResponse<DtoAiParseFileApp> restResponse = new RestResponse<>();
        DtoAiParseFileApp result = service.findOne(id);
        restResponse.setRestStatus(result != null ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * 根据仪器ID查询解析应用列表
     *
     * @param instrumentId 仪器ID
     * @return RestResponse<List<DtoAiParseFileApp>>
     */
    @ApiOperation(value = "根据仪器ID查询解析应用列表", notes = "根据仪器ID查询解析应用列表")
    @GetMapping("/instrument/{instrumentId}")
    public RestResponse<List<DtoAiParseFileApp>> findByInstrumentId(@PathVariable String instrumentId) {
        RestResponse<List<DtoAiParseFileApp>> restResponse = new RestResponse<>();
        List<DtoAiParseFileApp> result = service.findByInstrumentId(instrumentId);
        restResponse.setRestStatus(StringUtil.isEmpty(result) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * 根据解析状态查询解析应用列表
     *
     * @param parseStatus 解析状态
     * @return RestResponse<List<DtoAiParseFileApp>>
     */
    @ApiOperation(value = "根据解析状态查询解析应用列表", notes = "根据解析状态查询解析应用列表")
    @GetMapping("/status/{parseStatus}")
    public RestResponse<List<DtoAiParseFileApp>> findByParseStatus(@PathVariable Integer parseStatus) {
        RestResponse<List<DtoAiParseFileApp>> restResponse = new RestResponse<>();
        List<DtoAiParseFileApp> result = service.findByParseStatus(parseStatus);
        restResponse.setRestStatus(StringUtil.isEmpty(result) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * 删除AI仪器解析应用
     *
     * @param id 应用ID
     * @return RestResponse<String>
     */
    @ApiOperation(value = "删除AI仪器解析应用", notes = "删除AI仪器解析应用")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.delete(id);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData("删除成功");
        return restResponse;
    }
}
