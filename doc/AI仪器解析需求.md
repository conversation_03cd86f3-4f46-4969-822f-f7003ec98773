# AI仪器解析需求
## 1.需求描述
### 1.1 仪器解析菜单
> 菜单增加'AI仪器解析'模块（通过框架配置进行添加） 

![菜单列表.png](docImg/%E8%8F%9C%E5%8D%95%E5%88%97%E8%A1%A8.png)
### 1.2 AI仪器解析模块
#### 1.2.1 界面要求：
- 分左中右结构，左侧包括解析准备区域，中间为文件预览区域，右侧解析结果区域、解析过程显示区域。显示比例1:2:1
- 左侧区域：
  - 显示标题：解析应用列表
  - 文件上传：可对单个应用进行单个文件上传
  - 选择仪器（下拉）列表中选择：如7890A气相色谱仪等
  - 选择解析方式（单选下拉）列表中选择：图像识别、文本提取
  - 执行解析按钮，可勾选多个解析应用后进行解析触发后可点击文件查看解析过程
  - 解析状态与解析过程：需要进行实时渲染
- 右上区域：
  - 显示标题：解析结果
  - 显示按钮：保存
  - 列表：显示采集编号、分析项目、参数名称、参数结果、量纲，其中参数结果支持修改
- 右下区域：
  - 显示标题：解析过程
  - 横向流程图：开始->文本解析/图像识别->AI解析->结束
  - 执行到某一个步骤时，需要标蓝标识完成，默认为灰色图标
  - 点击步骤可以在右下方显示解析过程的内容文本信息
  - 中间：
  - 显示标题：文件预览，在上传文件后显示文件预览


注：图片与描述不符合（图片未重构），AI可根据下图以及上方描述进行重构
![AI仪器解析模块.png](docImg/AI%E4%BB%AA%E5%99%A8%E8%A7%A3%E6%9E%90%E6%A8%A1%E5%9D%97.png)

### 1.3 AI仪器解析配置（实验室）
#### 1.3.1 列表页面
 - 检索条件：仪器名称、仪器类型
 - 按钮：新增、删除、解析
 - 显示列表：仪器类型、仪器名称、仪器编号、所属单位、提示词

![AI仪器解析配置列表.png](docImg/AI%E4%BB%AA%E5%99%A8%E8%A7%A3%E6%9E%90%E9%85%8D%E7%BD%AE%E5%88%97%E8%A1%A8.png)

#### 1.3.2 仪器解析配置详情
界面要求：
- 标题：仪器解析详情
- 表单内容：
  - 仪器类型：下拉框，包括现场仪器、实验室仪器，针对现场仪器仅做预留
  - 仪器名称：必填，填写框
  - 仪器编号：填写框
  - 所属单位：预留，填写框
  - 提示词：富文本框
  
![AI仪器解析配置详情.png](docImg/AI%E4%BB%AA%E5%99%A8%E8%A7%A3%E6%9E%90%E9%85%8D%E7%BD%AE%E8%AF%A6%E6%83%85.png)

功能要求：
- 可针对不同仪器设置不同的提示词，示例：
```textmate
你需要对知识库中的数据进行解析，提取成json格式.
需要提取的内容包括：样品编号、分析项目、参数名称、参数值、单位。
json示例： { “sample_code” : "xxx", “AnalyzeItem” ："xxx", "parameter_value"：“xxx”, "unit":"xxx" }
注意事项：
1、每个参数结果为一块
2、Sample Name为样品编号
3、Peak Name为分析项目
4、Area、Height、Amount分别为峰面积、峰高、测得结果
```
#### 1.3.3 仪器解析日志
> 日志模块功能已存在
- 页面:
![仪器解析日志列表.png](docImg/%E4%BB%AA%E5%99%A8%E8%A7%A3%E6%9E%90%E6%97%A5%E5%BF%97%E5%88%97%E8%A1%A8.png)

- 需求描述：
  - 对AI解析的数据结果，需要在解析日志中可查看，每个文件对应一条解析记录