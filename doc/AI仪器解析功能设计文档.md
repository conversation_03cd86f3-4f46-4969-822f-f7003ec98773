# AI仪器解析功能设计文档

## 1. 需求设计

### 1.1 功能概述
AI仪器解析功能是基于人工智能技术的仪器数据解析系统，支持多种仪器类型的数据文件解析，通过图像识别和文本提取两种方式实现智能化数据提取。

### 1.2 功能模块

#### 1.2.1 AI仪器解析模块
**界面布局：**
- 左中右三栏布局，比例为1:2:1
- 左侧：解析应用列表区域
- 中间：文件预览区域  
- 右侧：解析结果区域和解析过程显示区域

**功能特性：**
- 每个解析应用对应一个解析文件（可选择多个解析应用同时进行批量解析）
- 批量解析时左侧解析应用列表的解析状态需要进行实时刷新
- 仪器类型选择（如7890A气相色谱仪等）
- 解析方式选择（图像识别、文本提取）
- 实时解析过程展示（解析过程以及对应的结果需要进行数据持久化）
- 解析结果编辑和保存（在未点击保存时，数据不会持久化到结果表中）

#### 1.2.2 AI仪器解析配置模块
**列表页面功能：**
- 仪器名称、仪器类型检索
- 新增、删除、解析操作
- 仪器信息展示（类型、名称、编号、所属单位、提示词）

**配置详情功能：**
- 仪器类型配置（现场仪器、实验室仪器）
- 仪器基本信息管理
- AI提示词配置
- 针对不同仪器的个性化解析规则设置

#### 1.2.3 解析日志模块
- 解析记录查看
- 每个文件对应一条解析记录
- 解析结果追溯

### 1.3 业务流程

#### 1.3.1 解析流程
1. 1.添加解析应用 → 2. 文件上传 → 3. 仪器选择 → 4. 解析方式选择 → 5. 勾选多个解析应用执行解析 → 6. 结果展示 → 7. 结果保存

#### 1.3.2 解析过程步骤
1. 开始
2. 文本解析/图像识别
3. AI解析
4. 结束

### 1.4 数据输出格式
解析结果包含以下字段：
- 采集编号（sample_code）
- 分析项目（AnalyzeItem）
- 参数名称（parameter_name）
- 参数结果（parameter_value）
- 量纲（unit）

## 2. 技术栈

### 2.1 后端技术栈
- **框架版本：** Spring Boot 1.5.15.RELEASE
- **Java版本：** Java 8
- **数据库：** MySQL/SQL Server/达梦数据库
- **ORM框架：** Spring Data JPA + MyBatis Plus
- **缓存：** Redis
- **安全框架：** Spring Security + JWT
- **数据库迁移：** Flyway
- **代码简化：** Lombok
- **API文档：** Swagger
- **构建工具：** Maven
- **长连接实时刷新：** SSE流式接口
- **多线程批量解析** 多线程编程

### 2.2 核心依赖组件
- **sinoyd-frame-core：** 框架核心组件
- **sinoyd-boot-starter-common：** 通用启动器
- **sinoyd-boot-starter-frame：** 框架启动器
- **grpc相关组件：** 支持gRPC通信
- **druid：** 数据库连接池

### 2.3 AI相关技术栈
- **文本提取：** 支持多种文档格式解析（TXT、Excel、Word、PDF、MDB）**（已封装：无需实现）**
- **图像识别：** OCR技术支持 **（已封装：无需实现）**
- **AI解析引擎：** 基于提示词的智能解析 **（已封装：无需实现）**
- **数据格式：** JSON格式数据交换

### 2.4 文件处理技术
- **文件上传：** Spring Boot MultipartFile
- **文件预览：** 支持多种格式文件预览 **（已封装：无需实现）**
- **文件存储：** 本地文件系统存储

### 2.5 前端技术栈（前后端分离，无需实现）
- **前端框架：** 基于现有框架系统
- **UI组件：** 支持文件上传、下拉选择、表格编辑等组件
- **数据交互：** RESTful API

## 3. 系统架构

### 3.1 分层架构
```
├── Controller层（接口层）
├── Service层（业务逻辑层）
├── Repository层（数据访问层）
├── Entity层（实体层）
└── VO/DTO层（数据传输层）
```

### 3.2 模块结构
```
├── arch层：接口定义
├── impl层：接口实现
├── public层：公用组件
```

### 3.3 核心服务组件
- **AI解析服务：** 负责调用AI引擎进行数据解析
- **文件处理服务：** 负责文件上传、预览、格式转换
- **仪器配置服务：** 负责仪器信息和解析规则管理
- **解析日志服务：** 负责解析过程和结果的记录

### 3.4 数据流向
```
文件上传 → 格式识别 → 内容提取 → AI解析 → 结果格式化 → 数据保存
```

## 4. 接口设计

### 4.1 RESTful API规范
- 遵循RESTful设计原则
- 统一API前缀：`/api/parse`
- 统一返回格式
- 统一异常处理

### 4.2 主要接口
#### 4.2.1 AI解析应用接口
- **AI解析应用接口：** `CRUD /api/parse/aiParse`

#### 4.2.2 文件上传接口
`POST /api/parse/aiParse/upload`

#### 4.2.3 AI解析接口
> 1. 多线程 每次最多同时3条数据进行解析
> 2. SSE流式接口`POST /api/parse/aiParse/execute`
- 输入:解析应用ID集合
- 流式输出：按照解析应用id与解析过程逐步输出数据（多线程处理）
 

#### 4.2.4 AI仪器解析配置接口
- **仪器配置接口：** `CRUD /api/parse/aiInstrumentConfig`


## 5. 性能设计

### 5.1 异步处理
- 文件解析异步处理
- 解析过程状态实时更新

