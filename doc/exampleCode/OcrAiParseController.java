package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.OcrConfigCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoOcrDataContainer;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfig;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigRecord;
import com.sinoyd.lims.lim.service.OcrConfigService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;

/**
 * OCRAI仪器解析示例Controller
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@RestController
@RequestMapping("api/lim/ocrConfig")
@Validated
@Slf4j
public class OcrAiParseController {

    private OcrAIParseServiceImpl ocrAIParseService;

    /**
     * ocr AI 解析流式接口(单个文件解析)
     *
     * @param ocrConfigRecord 识别参数
     * @return RestResponse<DtoOcrConfig>
     */
    @ApiOperation(value = "ocr接口测试", notes = "ocr接口测试")
    @PostMapping("/ocrStream")
    public SseEmitter callStreamApi(@RequestBody DtoOcrConfigRecord ocrConfigRecord) {
        SseEmitter emitter = new SseEmitter(0L); // 不超时
        SecurityContext context = SecurityContextHolder.getContext();
        // 在新线程中执行，避免阻塞主线程
        new Thread(() -> {
            try {
                SecurityContextHolder.setContext(context);
                ocrAIParseService.callStreamApi(ocrConfigRecord, data -> {
                    try {
                        // 发送数据到客户端
                        emitter.send(data);
                        log.info("Sent SSE data: {}", data);
                    } catch (IOException e) {
                        log.error("Failed to send SSE data", e);
                        emitter.completeWithError(e);
                    }
                });
                // 完成流式传输
                emitter.complete();
                log.info("SSE stream completed");
            } catch (Exception e) {
                log.error("OCR stream processing failed", e);
                emitter.completeWithError(e);
            } finally {
                // 无论成功或失败，都必须清除上下文
                SecurityContextHolder.clearContext();
                log.debug("子线程安全上下文已清理。");
            }
        }).start();
        return emitter;
    }
}
