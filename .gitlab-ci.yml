variables:
  TARGET_NAME: "parseWebbackend-0.0.1-SNAPSHOT"
  MAVEN_REPO_USERNAME: $MVN_USER  # 在GitLab设置Variables
  MAVEN_REPO_PASSWORD: $MVN_PWD


stages:
  - compile
  - nextcloud_upload # 发布到nextcloud
  - docker
  - restart

compile:jdk8:
  image: registry.dev.yd/sinoyd/registry/maven:3.8.4-openjdk-8-dn
  stage: compile
  tags:
    - dind
  script:
    - cp m2-settings.xml /root/.m2/settings.xml
    - 'mvn clean package'
    - ls target/
    - ls target/lib/
    - mkdir -p sartifacts/bin
    - cp target/$TARGET_NAME.jar sartifacts/bin/app.jar
    - cp Dockerfile sartifacts/
    - cp initlibs.sh sartifacts/bin/
    # - cp -r target/lib sartifacts/bin
    # - rm sartifacts/bin/lib/common-0.0.1-SNAPSHOT.jar
    - ls sartifacts/
    - ls sartifacts/bin
  artifacts:
    paths:
      - sartifacts/*
    expire_in: 1 day
  only:
    - master

upload-nextcloud:
  stage: nextcloud_upload
  image: registry.dev.yd/sinoyd/registry/gitlab-dingtalk
  variables:
    PLUGIN_TOKEN: $DINGTALK_TOKEN
    PLUGIN_TPL_FAILURE_IMAGE: https://cdn.ydlims.com/img/deploy-success.jpg
  script:
    - TODAY=$(date +%Y-%m-%d)
    - BASE_URL="http://nextcloud.dev.yd/remote.php/dav/files/${NEXTCLOUD_USER}"
    - |
      mk_dir_if_absent() {
        local dir="$1"
        local encoded=$(printf '%s' "$dir" \
          | sed 's/ /%20/g' \
          | sed 's/!/%21/g;s/"/%22/g;s/#/%23/g;s/\$/%24/g;s/\&/%26/g;s/'\''/%27/g' \
          | sed 's/(/%28/g;s/)/%29/g;s/\*/%2A/g;s/+/%2B/g;s/,/%2C/g' \
          | sed 's/:/%3A/g;s/;/%3B/g;s/</%3C/g;s/=/%3D/g;s/>/%3E/g;s/?/%3F/g;s/@/%40/g' \
          | sed 's/\[/%5B/g;s/\\/%5C/g;s/\]/%5D/g;s/\^/%5E/g;s/_/%5F/g;s/`/%60/g' \
          | sed 's/{/%7B/g;s/|/%7C/g;s/}/%7D/g;s/~/%7E/g')
        curl -s -u "${NEXTCLOUD_USER}:${NEXTCLOUD_PWD}" \
             -X PROPFIND "${BASE_URL}/${encoded}" \
        | grep -q '<d:multistatus>' \
        || curl -u "${NEXTCLOUD_USER}:${NEXTCLOUD_PWD}" \
                -X MKCOL "${BASE_URL}/${encoded}"
      }
    - mk_dir_if_absent "04_LIMS/5.3/稳定分支/instrument/后端/${TODAY}"
    - curl -u "$NEXTCLOUD_USER:$NEXTCLOUD_PWD" -T sartifacts/bin/app.jar -X PUT "http://nextcloud.dev.yd/remote.php/dav/files/$NEXTCLOUD_USER/04_LIMS/5.3/稳定分支/instrument/后端/${TODAY}/app.jar"
    - /app/push.sh -s -t app.jar -m "LIMS仪器解析后端发布成功，下载地址：http://nextcloud.dev.yd/remote.php/dav/files/$NEXTCLOUD_USER/04_LIMS/5.3/稳定分支/instrument/后端/$(date +%Y-%m-%d)/app.jar"
  only:
    - master

docker-build:
  image: docker:stable-dind
  stage: docker
  variables:
    GIT_STRATEGY: none
  tags:
    - docker
  script:
    - docker login --username=$DOCKER_USER -p "$DOCKER_PWD" registry.cn-shanghai.aliyuncs.com
    - docker pull registry.cn-shanghai.aliyuncs.com/dn/java:latest
    - docker build --compress -t "registry.cn-shanghai.aliyuncs.com/yuanda/sinoyd-instrumentparse:latest" -f sartifacts/Dockerfile ./sartifacts/
    - docker push registry.cn-shanghai.aliyuncs.com/yuanda/sinoyd-instrumentparse:latest
  only:
    - master

#docker-restart:
#  image: appropriate/curl
#  stage: restart
#  variables:
#    GIT_STRATEGY: none
#  tags:
#    - docker
#  script:
#    - ls -all sartifacts/
#    - curl -X POST $DOCKER_SERVICEHOOK
#  only:
#    - master
