<config>
	<input>
		<jar in="../target/sinoyd-parse-impl-0.0.1Cloud-SNAPSHOT.jar" out="../target/sinoyd-parse-impl-0.0.1Cloud-SNAPSHOT.jar" />
	</input>

	<keep-names>
		<class access="protected+">
			<field access="protected+" />
			<method access="protected+" parameters="keep" />
		</class>
	</keep-names>

	<ignore-classes>
		<class template="class *springframework*" />
		<class template="class *shardingjdbc*" />
		<class template="class *jni*" />
		<class template="class *persistence*" />
		<class template="class *activiti.engine*" />
	</ignore-classes>
</config>
