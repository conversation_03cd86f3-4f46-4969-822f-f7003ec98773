#app
server:
  port: ${PORT:8981}
  tomcat:
    uri-encoding: utf-8

frame-boot:
  swagger:
    enabled: true
    # controller包路径
    basePackage: com.sinoyd
    title: 快速开发基础平台
    description: 系统Rest接口API
  # 框架注册码服务校验地址
  regVerifyUrl: ${REG_VERIFY_URL:http://*************:8760/api/register/auth/verify}
  # 权限同步网关服务地址前缀
  gateUrlPrefix: ${GATE_URL_PREFIX:http://localhost:9091/api/proxy}
  # rest请求超时设置(单位：毫秒)
  restRequestTimeout: 3000
  # 互联网用户中心相关配置
  user-center:
    # 是否为本地离线模式(true: 离线模式、false：云模式)
    localMode: ${USER_CENTER_LOCALMODE:true}
    # 用户中心接口服务地址
    serverUrl: ${USER_CENTER_SERVERURL:http://**************:81/api/auth/users}
    # 客户端ID(云版模式需要配置)
    clientId: ${USER_CENTER_CLIENTID:8}
    # 客户端秘钥(云版模式需要配置)
    clientSecret: ${USER_CENTER_CLIENTSECRET:jIkjGEIfn7lYem52gcnfqHiLGkAEav98DfI5BRt4}
    # 短信内容中系统名称(云版模式需要配置)
    sendSmsProductName: ${USER_CENTER_SMSNAME:云框架}
  # 用户登录失败锁相关配置
  user-lock:
    enabled: false

# spring
spring:
  application:
    name: sinoyd-parse
  redis:
    host: ${REDIS_HOST:**************}
    port: ${REDIS_PORT:26303} # Redis服务器连接端口
    password: ${REDIS_PWD:sinoyd} # Redis服务器连接密码（默认为空）
    timeout: 10000
    database: 9


  # DATABASE CONFIG
  datasource:
    primary: # 业务的数据源
      # mysql连接配置
      # driver-class-name: com.mysql.jdbc.Driver
      # driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      driverClassName: dm.jdbc.driver.DmDriver
      # url: jdbc:dm://${DB_HOST:**************}:${DB_PORT:5342}/${DB_NAME_LIMS:INSTRUMENTPARSE}
      url: jdbc:dm://${DB_HOST:**************}:${DB_PORT:5342}?schema=${DB_NAME:INSTRUMENTPARSE}
      # url: ***************************************************************************************
      # url: jdbc:sqlserver://${SQLSER_HOST:*************\SQL2012}:${SQLSER_PORT:55488};DatabaseName=${SQLSER_DB_NAME_LIMS:InstrumentParse}
      username: INSTRUMENTPARSE
      password: yiqijiexi@123
      initialSize: 10
      minIdle: 5
      maxActive: 50
      maxWait: 60000
      testWhileIdle: true

    # 框架的数据源
    frame:
      # driverClassName: com.mysql.jdbc.Driver
      # driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      driverClassName: dm.jdbc.driver.DmDriver
      # url: jdbc:dm://${DB_HOST:**************}:${DB_PORT:5342}/${DB_NAME_FRAME:INSTRUMENTFRAME}
      url: jdbc:dm://${DB_HOST:**************}:${DB_PORT:5342}?schema=${DB_NAME:INSTRUMENTFRAME}
      # url: *********************************************************************************************
      # url: jdbc:sqlserver://${SQLSER_HOST:*************\SQL2012}:${SQLSER_PORT:55488};DatabaseName=${SQLSER_DB_NAME_LIMS:FrameInstrumentParse}
      username: INSTRUMENTFRAME
      password: jiexiframe@123
      maxActive: 100
      poolPreparedStatements: false
      filters: stat,log4j # 达梦
      connectionProperties: druid.stat.mergeSql=false;druid.stat.slowSqlMillis=5000
  jpa:
     # database: sql_server # oracle严重注意！
#     database-platform: org.hibernate.dialect.Oracle12cDialect # oracle 12c 严重注意！
#     database-platform: com.sinoyd.frame.CustomOracle12cDialect # oracle 12c 严重注意！
     # database-platform: com.sinoyd.frame.configuration.CustomMySQLDialect # mysql  严重注意！
#     database-platform: org.hibernate.dialect.SQLServer2012Dialect # sqlserver  严重注意！
     # database-platform: com.sinoyd.frame.configuration.CustomSQLServer2008JSONDialect # sqlserver  严重注意！
     database-platform: com.sinoyd.frame.configuration.CustomDMDialect # DMserver  严重注意！
     hibernate:
#       ddl-auto: update
       naming:
         implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
         physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
     show-sql: true
     properties:
       hibernate:
         session_factory:
           statement_inspector: com.sinoyd.frame.inspector.JpaInterceptor
         format_sql: true

  http:
  multipart:
    max-file-size: 10000MB
    max-request-size: 10000MB
    enabled: true

mybatis-plus:
  dialectType: dm #达梦数据库时候需要设置，其他数据库如mysql,sqlserver不需要设置

fileProps:
  filePath: ${FILE_PATH:E:/LIMS/InstrumentParse-dm/sinoyd-proxy/files}
  outputPath: ${FILE_OUTPUT_PATH:E:/LIMS/InstrumentParse-dm/sinoyd-proxy/output}

logging:
  level: info
  file: ${LOG4_PATH:d:/SpringBoot/SinoydLIMS-dm.log}

management:
  security:
    enabled: false

#用户和服务调用jwt相关设置
jwt:
  user:
    token-header: Authorization
    # 有效期（单位：分钟）
    expire: 120
