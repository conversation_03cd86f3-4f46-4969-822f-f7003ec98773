package com.sinoyd.common.http;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;

import javax.net.ssl.X509TrustManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * HTTP请求工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/6
 */
public class HTTPCaller {

    private static HTTPCaller caller = null;

    private OkHttpClient okHttpClient;

    private HTTPCaller() {
    }

    public static HTTPCaller getInstance() {
        return getInstance(false);
    }

    public static HTTPCaller getInstance(boolean isHttps) {
        if (caller == null) {
            caller = new HTTPCaller();
        }
        HttpConfig httpConfig = new HttpConfig();
        if (isHttps) {
            caller.okHttpClient = new OkHttpClient.Builder().sslSocketFactory(HttpsUtil.getSSLSocketFactory(),
                            (X509TrustManager) HttpsUtil.getTrustManager())
                    .connectTimeout(httpConfig.getConnectTimeout(), TimeUnit.SECONDS)
                    .writeTimeout(httpConfig.getWriteTimeout(), TimeUnit.SECONDS)
                    .readTimeout(httpConfig.getReadTimeout(), TimeUnit.SECONDS)
                    .build();
        } else {
            caller.okHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(httpConfig.getConnectTimeout(), TimeUnit.SECONDS)
                    .writeTimeout(httpConfig.getWriteTimeout(), TimeUnit.SECONDS)
                    .readTimeout(httpConfig.getReadTimeout(), TimeUnit.SECONDS)
                    .build();
        }
        return caller;
    }

    /**
     * 获取HttpClient对象
     *
     * @return OkHttpClient对象
     */
    public OkHttpClient getOkHttpClient() {
        return this.okHttpClient;
    }


    /**
     * get远程请求， 适用于返回单条数据，比如详情查询
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test/6CD54881-9A72-ABCD-B2F9-01C5118A2152
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     * @return 请求结果，以JSONObject形式返回
     */
    public JSONObject getOne(String gateUrl,
                             String url,
                             String token,
                             List<NameValuePair> paramList) {
        return get(gateUrl, url, token, paramList).getJSONObject("data");
    }

    /**
     * get远程请求， 适用于返回多条数据
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test/6CD54881-9A72-ABCD-B2F9-01C5118A2152
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     * @return 请求结果，以JSONArray形式返回
     */
    public JSONArray getList(String gateUrl,
                             String url,
                             String token,
                             List<NameValuePair> paramList) {
        return get(gateUrl, url, token, paramList).getJSONArray("data");
    }

    /**
     * post远程请求(适用返回单条数据)
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test/6CD54881-9A72-ABCD-B2F9-01C5118A2152
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     * @return 请求结果，以JSONObject形式返回
     */
    public JSONObject postOne(String gateUrl,
                              String url,
                              String token,
                              List<NameValuePair> paramList) {
        return post(gateUrl, url, token, paramList).getJSONObject("data");
    }

    /**
     * post远程请求(适用返回多条数据)
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test/6CD54881-9A72-ABCD-B2F9-01C5118A2152
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     * @return 请求结果，以JSONObject形式返回
     */
    public JSONArray postList(String gateUrl,
                              String url,
                              String token,
                              List<NameValuePair> paramList) {
        return post(gateUrl, url, token, paramList).getJSONArray("data");
    }

    /**
     * post远程请求(无需返回数据)
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     */
    public void postVoid(String gateUrl,
                         String url,
                         String token,
                         List<NameValuePair> paramList) {
        post(gateUrl, url, token, paramList);
    }


    /**
     * post远程请求
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     * @return 请求结果，以JSONObject形式返回
     */
    public JSONObject post(String gateUrl,
                           String url,
                           String token,
                           List<NameValuePair> paramList) {
        return JSONObject.parseObject(postAsString(gateUrl, url, token, paramList));
    }

    /**
     * put远程请求
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     * @return 请求结果，以JSONObject形式返回
     */
    public JSONObject put(String gateUrl,
                           String url,
                           String token,
                           List<NameValuePair> paramList) {
        List<NameValuePair> headerList = new ArrayList<>();
        headerList.add(new NameValuePair("Authorization", token));
        return JSONObject.parseObject(requestToString(gateUrl, url, headerList, paramList, "PUT"));
    }

    /**
     * put远程请求(适用返回多条数据)
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test/6CD54881-9A72-ABCD-B2F9-01C5118A2152
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     * @return 请求结果，以JSONObject形式返回
     */
    public JSONArray putList(String gateUrl,
                              String url,
                              String token,
                              List<NameValuePair> paramList) {
        List<NameValuePair> headerList = new ArrayList<>();
        headerList.add(new NameValuePair("Authorization", token));
        return put(gateUrl, url, token, paramList).getJSONArray("data");
    }

    /**
     * put远程请求(无需返回数据)
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     */
    public void putVoid(String gateUrl,
                         String url,
                         String token,
                         List<NameValuePair> paramList) {
        put(gateUrl, url, token, paramList);
    }

    /**
     * delete远程请求
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     * @return 请求结果，以JSONObject形式返回
     */
    public JSONObject delete(String gateUrl,
                          String url,
                          String token,
                          List<NameValuePair> paramList) {
        List<NameValuePair> headerList = new ArrayList<>();
        headerList.add(new NameValuePair("Authorization", token));
        return JSONObject.parseObject(requestToString(gateUrl, url, headerList, paramList, "DELETE"));
    }

    /**
     * post远程请求
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     * @return 请求结果，以字符串形式返回
     */
    public String postAsString(String gateUrl,
                               String url,
                               String token,
                               List<NameValuePair> paramList) {
        List<NameValuePair> headerList = new ArrayList<>();
        headerList.add(new NameValuePair("Authorization", token));
        return postAsString(gateUrl, url, headerList, paramList);
    }

    /**
     * post远程请求
     *
     * @param gateUrl    网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url        实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test
     * @param headerList 请求头
     * @param paramList  参数集合
     * @return 请求结果，以字符串形式返回
     */
    public String postAsString(String gateUrl,
                               String url,
                               List<NameValuePair> headerList,
                               List<NameValuePair> paramList) {
        return requestToString(gateUrl, url, headerList, paramList, "POST");
    }

    /**
     * get远程请求
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test/6CD54881-9A72-ABCD-B2F9-01C5118A2152
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     * @return 请求结果，以JSONObject形式返回
     */
    public JSONObject get(String gateUrl,
                          String url,
                          String token,
                          List<NameValuePair> paramList) {
        return JSONObject.parseObject(getJsonString(gateUrl, url, token, paramList));
    }

    /**
     * get远程请求
     *
     * @param gateUrl   网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url       实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test/6CD54881-9A72-ABCD-B2F9-01C5118A2152
     * @param token     身份令牌，如果不需要验证，可以传null
     * @param paramList 参数集合
     * @return 请求结果，返回json字符串或直接字符串结果
     */
    public String getJsonString(String gateUrl,
                                String url,
                                String token,
                                List<NameValuePair> paramList) {
        List<NameValuePair> headerList = new ArrayList<>();
        headerList.add(new NameValuePair("Authorization", token));
        return getJsonString(gateUrl, url, headerList, paramList);
    }

    /**
     * get远程请求
     *
     * @param gateUrl    网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url        实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test/6CD54881-9A72-ABCD-B2F9-01C5118A2152
     * @param headerList 请求头
     * @param paramList  参数集合
     * @return 请求结果，返回json字符串或直接字符串结果
     */
    public String getJsonString(String gateUrl,
                                String url,
                                List<NameValuePair> headerList,
                                List<NameValuePair> paramList) {
        return requestToString(gateUrl, url, headerList, paramList, "GET");
    }

    /**
     * 通用远程请求
     *
     * @param gateUrl    网关地址，一定要http或者https开头，比如http://192.168.12.96:6001
     * @param url        实际请求地址，除网关地址外的部分， 比如 /api/sinoyd-lims/lim/test/6CD54881-9A72-ABCD-B2F9-01C5118A2152
     * @param headerList 请求头
     * @param paramList  参数集合
     * @param httpMethod 请求方法，比如GET、POST、PUT、DELETE
     * @return 请求结果，返回json字符串或直接字符串结果
     */
    private String requestToString(String gateUrl,
                                   String url,
                                   List<NameValuePair> headerList,
                                   List<NameValuePair> paramList,
                                   String httpMethod) {
        if (!url.startsWith("/")) {
            url = "/" + url;
        }
        Request.Builder requestBuilder = new Request.Builder();
        if (headerList != null && !headerList.isEmpty()) {
            headerList.forEach(header -> requestBuilder.addHeader(header.getName(), header.getValue().toString()));
        }
        Request request;
        if ("GET".equals(httpMethod)) {
            url = gateUrl + HttpClientUtil.addParam2Url(url, paramList);
            request = requestBuilder.url(url).get().build();
        } else {
            url = gateUrl + url;
            RequestBody requestBody = RequestBody.create(HttpClientUtil.params2Json(paramList), MediaType.parse("application/json;charset=utf-8"));
            if ("POST".equals(httpMethod)) {
                request = requestBuilder.url(url).post(requestBody).build();
            } else if ("PUT".equals(httpMethod)) {
                request = requestBuilder.url(url).put(requestBody).build();
            } else {
                request = requestBuilder.url(url).delete(requestBody).build();
            }
        }
        try (Response response = this.okHttpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                ResponseBody responseBody = response.body();
                return Objects.requireNonNull(responseBody).string();
            } else {
                throw new RuntimeException(String.format("......remote call happened error, please refer detail error message: [response code: %s, request method : %s, request url: %s, error message : %s]",
                        response.code(), httpMethod, url, response.message()));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}