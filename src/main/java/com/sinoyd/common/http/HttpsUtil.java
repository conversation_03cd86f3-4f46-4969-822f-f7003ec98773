package com.sinoyd.common.http;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;

/**
 * https信任相关工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/7/2
 */
public class HttpsUtil {

    /**
     *
     * @return 连接工厂
     */
    public static SSLSocketFactory getSSLSocketFactory() {
        try {
            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, getTrustManagerArr(), new SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 信任所有
     * @return 信任策略
     */
    public static TrustManager[] getTrustManagerArr() {
        return new TrustManager[]{getTrustManager()};
    }

    /**
     * 信任所有
     * @return 信任策略
     */
    public static TrustManager getTrustManager() {
        return new X509TrustManager() {
            //检查客户端证书，若不信任该证书抛出异常，咱们自己就是客户端不用检查
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }
            //检查服务器的证书，若不信任该证书抛出异常，可以不检查默认都信任
            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }
            //返回受信任的X509证书数组
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }
        };
    }
}