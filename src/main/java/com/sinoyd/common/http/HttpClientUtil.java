package com.sinoyd.common.http;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;
import java.util.List;

/**
 * http远程调用工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/6
 */
public class HttpClientUtil {

    /**
     * 将请求参数转换成json形式
     *
     * @param paramList 请求参数列表
     * @return 请求参数json形式
     */
    public static String params2Json(List<NameValuePair> paramList) {
        if (paramList != null && !paramList.isEmpty()) {
            JSONObject jsonObject = new JSONObject();
            if (paramList.size() == 1 && "".equals(paramList.get(0).getName()) && paramList.get(0).getValue() instanceof java.util.Collection) {
                return JSONObject.toJSONString(paramList.get(0).getValue());
            } else {
                for (NameValuePair pair : paramList) {
                    jsonObject.put(pair.getName(), pair.getValue());
                }
                return jsonObject.toJSONString();
            }
        }
        return "";
    }

    /**
     * 将请求参数拼接到url后面
     *
     * @param url       请求地址
     * @param paramList 参数集合
     * @return 拼接后的url
     */
    public static String addParam2Url(String url, List<NameValuePair> paramList) {
        if (StringUtils.isEmpty(url))
            return "";
        if (url.contains("?")) {
            url = url + "&";
        } else {
            url = url + "?";
        }
        url += transferParam2String(paramList);
        return url;
    }

    /**
     * 将请求参数进行拼接
     *
     * @param paramList 请求参数
     * @return 拼接后的参数
     */
    private static String transferParam2String(List<NameValuePair> paramList) {
        StringBuilder sb = new StringBuilder();
        if (paramList == null || paramList.isEmpty()) {
            return sb.toString();
        }
        try {
            int i = 0;
            for (NameValuePair item : paramList) {
                if (i > 0) {
                    sb.append("&");
                }
                sb.append(item.getName());
                sb.append('=');
                if (item.getValue() != null) {
                    sb.append(URLEncoder.encode(item.getValue().toString(), "utf-8"));
                }
                i++;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return sb.toString();
    }
}