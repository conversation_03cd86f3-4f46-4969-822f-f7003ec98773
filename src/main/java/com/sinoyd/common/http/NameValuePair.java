package com.sinoyd.common.http;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 请求参数
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/6
 */
@Data
@Accessors(chain = true)
public class NameValuePair {

    /**
     * 请求参数名
     */
    private String name;

    /**
     * 请求参数值
     */
    private Object value;

    public NameValuePair() {
        super();
    }

    public NameValuePair(String name, Object value) {
        super();
        this.name = name;
        this.value = value;
    }
}