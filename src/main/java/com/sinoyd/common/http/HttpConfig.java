package com.sinoyd.common.http;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 通用http请求参数
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/6
 */
@Data
@Accessors(chain = true)
public class HttpConfig {

    /**
     * 连接超时时间 单位:秒
     */
    private int connectTimeout = 10;

    /**
     * 写入超时时间 单位:秒
     */
    private int writeTimeout = 10;

    /**
     * 读取超时时间 单位:秒
     */
    private int readTimeout = 30;

}