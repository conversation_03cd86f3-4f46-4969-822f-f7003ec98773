package com.sinoyd.common.revise;

import com.sinoyd.common.utils.ReviseUtil;

/**
 * 数据修约实现类: 对非0.开头的小数进行修约
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/4/27
 */
public class ReviseDecimalUnZeroStartData implements IReviseData {
    @Override
    public String revise(String value, Integer sign, Integer scale, boolean isSci) {
        //如果有效位是空或者-1，则不对有效位进行修约，直接按小数为修约
        if (sign == null || sign == -1) {
            return ReviseUtil.revise(value, scale);
        }
        //原始数据有效位
        int valueSign = ReviseUtil.calculateValueSign(value);
        //将原始数据按小数点拆分
        String[] valueArray = value.split("\\.");
        String intPart = valueArray[0];
        String decimalPart = valueArray[1];
        //整数部分有效位
        int intPartSign = ReviseUtil.calculateValueSign(intPart);
        if (valueSign > sign) {
            //整数部分有效位 > sign且修约结果无需转科学计数法，需要转换科学计数法
            if (intPartSign > sign && !isSci) {
                //科学计数法
                String eValue = ReviseUtil.toScientificNotation(value);
                //对科学计数法按E拆分
                String[] eValueArray = eValue.split("E");
                //拆分后的非指数部分
                value = eValueArray[0];
                //拆分后的指数部分
                String index = eValueArray[1];
                //如果转换科学技术法后按有效位修约，value有效位已经超出了sign或者scale = -1，则scale会变为有效位 - 1
                if (ReviseUtil.calculateValueSign(value) > sign || scale == -1) {
                    scale = sign - 1;
                }
                value = ReviseUtil.revise(value, scale) + "E" + index;
            } else {
                //整数部分有效位 <= sign，此时需要结合小数部分来重新计算scale
                int newScale = sign - intPartSign;
                //如果新的小数位不大于scale，则按新新小数位修约
                if (newScale <= scale || scale == -1) {
                    scale = newScale;
                }
                value = ReviseUtil.revise(value, scale);
            }
        } else if (valueSign < sign) {
            //原始数据有效位 < sign，需要在末位加0补齐
            StringBuilder valueBuilder = new StringBuilder(value);
            for (int i = valueSign; i < sign; i++) {
                valueBuilder.append("0");
            }
            value = valueBuilder.toString();
            int decimalLength = value.split("\\.")[1].length();
            if (decimalLength < scale) {
                scale = decimalLength;
            }
            value = ReviseUtil.revise(value, scale);
        } else {
            //原始数据有效位和小数位相等，需要看原始数据的小数位是不是超过了scale，超过了则需要按scale修约，反之按原始数据小数位修约
            int decimalLen = decimalPart.length();
            if (decimalLen <= scale) {
                scale = decimalLen;
            }
            value = ReviseUtil.revise(value, scale);
        }

        return value;
    }

}