package com.sinoyd.common.revise;

/**
 * 修约枚举类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/19
 */
public enum EnumRevise {

    /**
     * 对非0.开头的小数进行修约
     */
    UN_ZERO_START_DECIMAL(new ReviseDecimalUnZeroStartData()),

    /**
     * 对0.开头的小数进行修约
     */
    ZERO_START_DECIMAL(new ReviseDecimalZeroStartData()),

    /**
     * 对整数进行修约
     */
    INT(new ReviseIntData()),

    /**
     * 对有效位为0的原始数据，比如0， 0.00之类
     */
    ZERO_SIGN_DATA(new ReviseZeroSignData());

    private final IReviseData reviseData;

    EnumRevise(IReviseData reviseData) {
        this.reviseData = reviseData;
    }

    public IReviseData getReviseData() {
        return reviseData;
    }
}