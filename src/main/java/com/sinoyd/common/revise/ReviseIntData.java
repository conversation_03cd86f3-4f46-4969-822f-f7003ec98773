package com.sinoyd.common.revise;

import com.sinoyd.common.utils.ReviseUtil;

/**
 * 数据修约实现类: 对整数进行修约
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/4/27
 */
public class ReviseIntData implements IReviseData {

    @Override
    public String revise(String value, Integer sign, Integer scale, boolean isSci) {
        //如果有效位是空或者-1，则不对有效位进行修约，直接按小数为修约
        if (sign == null || sign == -1) {
            return ReviseUtil.revise(value, scale);
        }
        //计算原始数据的有效位
        int valueSign = ReviseUtil.calculateValueSign(value);
        //如果原始数据有效位大于sign，则需要转换成科学技术法，再对科学计数法的非指数部分进行修约
        if (valueSign > sign && !isSci) {
            String eValue = ReviseUtil.toScientificNotation(value);
            //按E对科学技术法进行拆分
            String[] eValueArray = eValue.split("E");
            value = eValueArray[0];
            value = ReviseDataFactory.revise(value, sign, scale, false);
            value = value + "E" + eValueArray[1];
        } else if (valueSign < sign) {
            value += ".";
            StringBuilder valueBuilder = new StringBuilder(value);
            for (int i = valueSign; i < sign; i++) {
                valueBuilder.append("0");
            }
            value = valueBuilder.toString();
            //如果value小数位大于scale，则需要按scale再修约
            if (value.contains(".") && value.split("\\.")[1].length() > scale) {
                value = ReviseUtil.revise(value, scale);
            }
        }
        return value;
    }

}