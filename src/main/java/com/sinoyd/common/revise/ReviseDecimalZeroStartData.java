package com.sinoyd.common.revise;

import com.sinoyd.common.utils.ReviseUtil;

/**
 * 数据修约实现类: 对0.开头的小数进行修约
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/4/27
 */
public class ReviseDecimalZeroStartData implements IReviseData {

    @Override
    public String revise(String value, Integer sign, Integer scale, boolean isSci) {
        //如果有效位是空或者-1，则不对有效位进行修约，直接按小数为修约
        if (sign == null || sign == -1) {
            return ReviseUtil.revise(value, scale);
        }
        //原始数据有效位
        int valueSign = ReviseUtil.calculateValueSign(value);
        //检查小数点后面0的个数，如果0的个数 > scale，则需要转换成科学计数法
        int zeroCountAfterPoint = ReviseUtil.calculateZeroCountAfterPoint(value);
        if (zeroCountAfterPoint > scale && scale != -1 && !isSci) {
            String eValue = ReviseUtil.toScientificNotation(value);
            //对科学计数法值eValue按E拆分
            String[] eValueArray = eValue.split("E");
            value = ReviseDataFactory.revise(eValueArray[0], sign, scale, false) + "E" + eValueArray[1];
        } else {
            //按有效位sign要求进行末位补0
            StringBuilder valueBuilder = new StringBuilder(value);
            for (int i = valueSign; i < sign; i++) {
                valueBuilder.append("0");
            }
            value = valueBuilder.toString();
            //对补齐后的value按有效位、小数位修约，如果修约后的小数位超出了scale，则需要重新计算小数位并进行修约
            String tempValue = ReviseUtil.revise(value, scale);

            if (tempValue.contains(".") && ReviseUtil.calculateValueSign(tempValue.split("\\.")[1]) > sign) {
                scale = sign + ReviseUtil.calculateZeroCountAfterPoint(value);
            }
            value = ReviseUtil.revise(value, scale);
        }
        return value;
    }

}