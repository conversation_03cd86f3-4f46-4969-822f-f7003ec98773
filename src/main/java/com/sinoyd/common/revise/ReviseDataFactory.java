package com.sinoyd.common.revise;

import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.common.utils.ReviseUtil;

import java.math.BigDecimal;

/**
 * 数据修约上下文实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/4/28
 */
public class ReviseDataFactory {

    /**
     * 修约工厂方法
     *
     * @param value 原始值
     * @param sign  有效位
     * @param scale 小数位
     * @param isSci 是否显示科学计数法
     * @return 修约结果
     */
    public static String revise(String value, Integer sign, Integer scale, boolean isSci) {
        //有效位、小数位全部是null或者-1，或者原始值是非数字则不修约，直接返回原始数据
        boolean notRevise = (sign == null || sign == -1) && (scale == null || scale == -1);
        boolean isNumber = MathUtil.isNumber(value);
        if (notRevise || !isNumber) {
            return value;
        }
        validation(sign, scale);
        if (!isSci) {
            return revise(value, sign, scale);
        } else {
            value = ReviseUtil.toScientificNotation(value);
            String numberPart = value.split("E")[0];
            String sciPart = value.split("E")[1];
            return revise(numberPart, sign, scale) + "E" + sciPart;
        }
    }

    /**
     * 修约工厂方法
     *
     * @param value 原始值
     * @param sign  有效位
     * @param scale 小数位
     * @return 修约结果
     */
    private static String revise(String value, Integer sign, Integer scale) {
        //如果原始数据是科学计数法，则先转换成数字
        if (value.contains("E")) {
            BigDecimal bd = new BigDecimal(value);
            value = bd.toPlainString();
        }
        if (ReviseUtil.calculateValueSign(value) == 0) {
            //对有效位为0的数据进行修约
            return EnumRevise.ZERO_SIGN_DATA.getReviseData().revise(value, sign, scale, false);
        } else if (!value.contains(".")
                || (!value.startsWith("0.") && value.contains(".") && ReviseUtil.calculateValueSign(value.split("\\.")[1]) == 0)) {
            //对整数修约, 无小数点或者诸如 xxx.0000的形式
            if (value.contains(".")) value = value.split("\\.")[0];
            return EnumRevise.INT.getReviseData().revise(value, sign, scale, false);
        } else if (value.startsWith("0.") || value.startsWith("-0.")) {
            //对0.或-0.开头的小数修约
            return EnumRevise.ZERO_START_DECIMAL.getReviseData().revise(value, sign, scale, false);
        } else {
            //非0.开头的小数修约
            return EnumRevise.UN_ZERO_START_DECIMAL.getReviseData().revise(value, sign, scale, false);
        }
    }

    /**
     * 有效位、小数位校验
     *
     * @param sign  有效位
     * @param scale 小数位
     */
    private static void validation(Integer sign, Integer scale) {
        if (sign == 0 && scale == 0) {
            throw new RuntimeException("有效位和小数位不能同时为0，否则无法进行修约计算");
        }
    }

}