package com.sinoyd.common.preview;

/**
 * 文件预览枚举类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/20
 */
public enum EnumDocumentPreview {

    /**
     * Excel预览
     */
    EXCEL(new ExcelDocumentPreviewer()),

    /**
     * Word预览
     */
    WORD(new WordDocumentPreviewer()),

    /**
     * PDF预览
     */
    PDF(new PDFDocumentPreviewer());


    private final IDocumentPreviewer previewer;

    EnumDocumentPreview(IDocumentPreviewer previewer) {
        this.previewer = previewer;
    }

    public IDocumentPreviewer getPreviewer() {
        return previewer;
    }
}