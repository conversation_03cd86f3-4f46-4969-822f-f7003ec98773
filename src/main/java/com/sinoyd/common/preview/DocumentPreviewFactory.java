package com.sinoyd.common.preview;

import com.sinoyd.common.vo.DocumentPreviewVO;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 文件预览工厂类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/20
 */
public class DocumentPreviewFactory {

    /**
     * excel文件格式
     */
    private static final List<String> EXCEL_SUFFIX_LIST = Arrays.asList("xls", "xlsx");

    /**
     * word文件格式
     */
    private static final List<String> WORD_SUFFIX_LIST = Arrays.asList("doc", "docx", "rtf");

    /**
     * pdf文件格式
     */
    private static final List<String> PDF_SUFFIX_LIST = Collections.singletonList("pdf");

    /**
     * 文件预览，转换成pdf形式预览
     *
     * @param rootPath 源文件根路径
     * @param vo       文件预览模型
     * @param response 响应流
     */
    public static void previewAsPDF(String rootPath, DocumentPreviewVO vo, HttpServletResponse response) {
        String docSuffix = vo.getSourceDocSuffix().replace(".", "");
        if (EXCEL_SUFFIX_LIST.contains(docSuffix.toLowerCase())) {
            EnumDocumentPreview.EXCEL.getPreviewer().previewAsPDF(rootPath, vo, response);
        } else if (WORD_SUFFIX_LIST.contains(docSuffix.toLowerCase())) {
            EnumDocumentPreview.WORD.getPreviewer().previewAsPDF(rootPath, vo, response);
        } else if(PDF_SUFFIX_LIST.contains(docSuffix.toLowerCase())) {
            EnumDocumentPreview.PDF.getPreviewer().previewAsPDF(rootPath, vo, response);
        }else {
            throw new RuntimeException("文件格式暂不支持预览");
        }
    }

    /**
     * Excel文件预览，转换成html形式预览
     *
     * @param os       字节数组输出流
     * @param response 响应流
     */
    public static void previewExcelAsHtml(ByteArrayOutputStream os, HttpServletResponse response) {
        EnumDocumentPreview.EXCEL.getPreviewer().previewAsHtml(os, response);
    }

    /**
     * Word文件预览，转换成html形式预览
     *
     * @param os       字节数组输出流
     * @param response 响应流
     */
    public static void previewWordAsHtml(ByteArrayOutputStream os, HttpServletResponse response) {
        EnumDocumentPreview.WORD.getPreviewer().previewAsHtml(os, response);
    }
}