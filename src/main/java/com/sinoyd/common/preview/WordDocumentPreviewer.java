package com.sinoyd.common.preview;

import com.aspose.words.Document;
import com.sinoyd.common.vo.DocumentPreviewVO;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 * Word文档预览器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/20
 */
public class WordDocumentPreviewer implements IDocumentPreviewer {

    @Override
    public void previewAsPDF(String rootPath, DocumentPreviewVO vo, HttpServletResponse response) {
        verifyLicense();
        loadPreviewFileInfo(rootPath, vo);
        String sourceFullPath = rootPath + vo.getSourceFilePath();
        com.aspose.words.Document doc;
        try {
            doc = new com.aspose.words.Document(sourceFullPath);
            //设置word标题
            doc.getBuiltInDocumentProperties().setTitle(vo.getSourceFileName());
            doc.save(vo.getPreviewFileFullPath(), com.aspose.words.SaveFormat.PDF);
        } catch (Exception e) {
            throw new RuntimeException("Word文件预览失败");
        }
        postProcessing(vo, response);
    }

    @Override
    public void previewAsHtml(ByteArrayOutputStream os, HttpServletResponse response) {
        try {
            InputStream byteArrayInputStream = new ByteArrayInputStream(os.toByteArray());
            Document document = new Document(byteArrayInputStream);
            com.aspose.words.HtmlSaveOptions ops = new com.aspose.words.HtmlSaveOptions(com.aspose.words.SaveFormat.HTML);
            ops.setExportImagesAsBase64(Boolean.TRUE);
            ops.setExportPageMargins(Boolean.TRUE);
            document.save(response.getOutputStream(), ops);
        } catch (Exception e) {
            throw new RuntimeException("Word文件预览失败");
        }
    }
}