package com.sinoyd.common.preview;

import com.aspose.cells.SaveFormat;
import com.aspose.cells.Workbook;
import com.aspose.cells.WorkbookDesigner;
import com.sinoyd.common.vo.DocumentPreviewVO;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 * Excel文档预览器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/19
 */
public class ExcelDocumentPreviewer implements IDocumentPreviewer {

    @Override
    public void previewAsPDF(String rootPath, DocumentPreviewVO vo, HttpServletResponse response) {
        verifyLicense();
        loadPreviewFileInfo(rootPath, vo);
        String sourceFullPath = rootPath + vo.getSourceFilePath();
        WorkbookDesigner designer = new WorkbookDesigner();
        try {
            System.out.println("...... 文件获取地址：" + sourceFullPath + " ......... ");
            Workbook workbook = new Workbook(sourceFullPath);
            // 设置excel文档属性标题
            workbook.getBuiltInDocumentProperties().setTitle(vo.getPreviewFileName());
            System.out.println("...... 设置标题 ......... ");
            designer.setWorkbook(workbook);
            System.out.println("...... 文件保存地址：" + vo.getPreviewFileFullPath() + " ......... ");
            designer.getWorkbook().save(vo.getPreviewFileFullPath(), com.aspose.cells.SaveFormat.PDF);
        } catch (Exception e) {
            System.out.println("...... 报错内容：" + e.getMessage() + " ......... ");
            throw new RuntimeException("Excel文件预览失败");
        }
        postProcessing(vo, response);
    }

    @Override
    public void previewAsHtml(ByteArrayOutputStream os, HttpServletResponse response) {
        try {
            InputStream is = new ByteArrayInputStream(os.toByteArray());
            WorkbookDesigner designer = new WorkbookDesigner();
            //清除工作区域
            designer.getWorkbook().getWorksheets().clear();
            designer.setWorkbook(new Workbook(is));
            designer.getWorkbook().save(response.getOutputStream(), SaveFormat.HTML);
        } catch (Exception e) {
            throw new RuntimeException("Excel文件预览失败");
        }
    }
}