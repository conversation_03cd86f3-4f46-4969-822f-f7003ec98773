package com.sinoyd.common.algorithm.comparison.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作运算符枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/07
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumOperationSymbol {

    大于(">", " > "),

    大于等于(">=", " >= "),

    小于("<", " < "),

    小于等于("<=", " <= "),

    等于("=", " = "),

    不等于("!=", " != "),

    并且("and", " && "),

    或("or", " || ");

    /**
     * 公式中的运算符
     */
    private final String value;

    /**
     * 解析后的运算符
     */
    private final String parseValue;
}
