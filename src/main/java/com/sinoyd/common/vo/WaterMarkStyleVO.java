package com.sinoyd.common.vo;

import com.aspose.pdf.HorizontalAlignment;
import com.aspose.pdf.VerticalAlignment;
import lombok.Data;
import lombok.experimental.Accessors;

import java.awt.*;

@Data
@Accessors(chain = true)
public class WaterMarkStyleVO {

    /**
     * 水印内容
     */
    private String markContent;

    /**
     * 水印颜色
     */
    private Color color;

    /**
     * 字体大小
     */
    private Float textSize = 50.0F;

    /**
     * 水印显示垂直样式
     */
    private Integer artifactHorizontal = HorizontalAlignment.Center;

    /**
     * 水印显示水平样式
     */
    private Integer artifactVertical = VerticalAlignment.Center;

    /**
     * 字体旋转
     */
    private Double rotation = -45.0;

    /**
     * 透明度
     */
    private Double opacity = 0.2;

    /**
     * 调整宽度
     */
    private Float width = 0.36f;

    /**
     * 第一个水印位置
     */
    private Float firstSize = 3f;

    /**
     * 第二个水印位置
     */
    private Float secondSize = 1.2f;
}
