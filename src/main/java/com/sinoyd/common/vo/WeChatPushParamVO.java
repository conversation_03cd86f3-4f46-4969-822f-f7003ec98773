package com.sinoyd.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 微信推送工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/24
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WeChatPushParamVO {

    /**
     * 推送地址
     */
    private String url;

    /**
     * 数据中心颁发的唯一标识
     */
    private String appId;

    /**
     * 数据中心颁发的唯一密钥
     */
    private String appSecret;

    /**
     * 微信群id
     */
    private String roomId;

    /**
     * 微信群名称
     */
    private String roomName;

    /**
     * 微信用户id
     */
    private String userId;

    /**
     * 微信用户名称
     */
    private String userName;

    /**
     * 推送消息内容
     */
    private String message;
}
