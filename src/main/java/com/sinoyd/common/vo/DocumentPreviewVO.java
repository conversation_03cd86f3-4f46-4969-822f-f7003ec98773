package com.sinoyd.common.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.InputStream;

/**
 * 文件预览VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/19
 */
@Data
@Accessors(chain = true)
public class DocumentPreviewVO {

    /**
     * 源文件后缀名
     */
    private String sourceDocSuffix;

    /**
     * 源文件路径，相对路径
     */
    private String sourceFilePath;

    /**
     * 源文件名
     */
    private String sourceFileName;

    /**
     * 预览的临时文件名
     */
    private String previewFileName;

    /**
     * 预览的临时文件全路径
     */
    private String previewFileFullPath;

    /**
     * 是否添加水印
     */
    private Boolean isAddWaterMark = false;

    /**
     * 设置水印
     */
    private WaterMarkStyleVO styleVO;

    /**
     *  文件状态图片文件流
     */
    private InputStream inputStream;

    /**
     *  原始文件标识
     */
    private String originId;
}