package com.sinoyd.common.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * MD5加密工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/24
 **/
public class MD5Util {

    /**
     * 对输入的字符串进行MD5编码，返回编码后的十六进制字符串。
     *
     * @param input 需要编码的字符串
     * @return 编码后的十六进制字符串
     * @throws RuntimeException 如果MD5算法不可用，则抛出运行时异常
     */
    public static String encode(String input) {
        try {
            // 创建 MessageDigest 实例，并指定使用 MD5 算法
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 将字符串转换为字节数组
            byte[] messageDigest = md.digest(input.getBytes());
            // 创建 StringBuilder 来保存加密后的十六进制字符串
            StringBuilder hexString = new StringBuilder();
            // 将字节数组转换为十六进制字符串
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
}
