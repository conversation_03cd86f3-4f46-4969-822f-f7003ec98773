package com.sinoyd.common.utils;


import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 计算相关工具类
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
@SuppressWarnings({"unused"})
public class MathUtil {

    public final static Pattern NUMBER_PATTERN = java.util.regex.Pattern.compile("^-?\\d+(\\.\\d+)?$");

    /**
     * 下限符号
     */
    public static final List<String> LOWER_LIMIT_SYMBOL = new ArrayList<String>() {{
        add("≥");
        add(">=");
        add(">");
    }};

    /**
     * 上限符号
     */
    public static final List<String> UPPER_LIMIT_SYMBOL = new ArrayList<String>() {{
        add("≤");
        add("<=");
        add("<");
    }};

    /**
     * 判断对象是否是数字
     *
     * @param value 对象值
     * @return 结果
     */
    public static boolean isNumber(Object value) {
        if (value != null) {
            if (value instanceof Number) {
                return true;
            } else if (value instanceof String) {
                String valueStr = (String) value;
                return isNumber(valueStr);
            }
        }
        return false;
    }

    /**
     * 字符串是否是数字
     *
     * @param string 字符串
     * @return 是否数字
     */
    public static boolean isNumber(String string) {
        try {
            new BigDecimal(string);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 求和计算
     *
     * @param numberList 原始数据集合
     * @return 结果
     */
    public static String calculateSum(Collection<String> numberList) {
        if (numberList == null || numberList.isEmpty()) {
            throw new RuntimeException("均值计算时接收的原始数据集合是空");
        }
        numberList.forEach(p -> {
            if (!isNumber(p)) {
                throw new RuntimeException("均值计算时，原始数据集合中存在非数字");
            }
        });

        BigDecimal total = new BigDecimal("0");
        for (String num : numberList) {
            total = new BigDecimal(num).add(total);
        }
        return total.toPlainString();
    }

    /**
     * 均值计算
     *
     * @param numberList 原始数据集合
     * @return 结果
     */
    public static String calculateAvg(Collection<String> numberList) {
        BigDecimal total = new BigDecimal(calculateSum(numberList));
        return total.divide(BigDecimal.valueOf(numberList.size()), 10, RoundingMode.HALF_UP).toPlainString();
    }

    /**
     * 计算传入的一系列数字的平均值，并返回结果作为字符串。
     *
     * @param numbers 一个可变参数，表示要计算平均值的数字列表。每个数字都是一个字符串。
     * @return 计算得到的平均值，作为字符串返回。
     */
    public static String calculateAvg(String... numbers) {
        return calculateAvg(Arrays.asList(numbers));
    }

    /**
     * 求差
     *
     * @param number_1 被减数
     * @param number_2 减数
     * @return 差
     */
    public static String subtract(String number_1, String number_2) {
        if (!isNumber(number_1) || !isNumber(number_2)) {
            throw new RuntimeException("对差计算原始值存在非数字或空值");
        }
        BigDecimal bd_1 = new BigDecimal(number_1);
        BigDecimal bd_2 = new BigDecimal(number_2);
        return bd_1.subtract(bd_2).toPlainString();
    }


    /**
     * 判断数值是否在范围内
     *
     * @param target 判断值
     * @param start  范围开始
     * @param end    范围结束
     * @return 结果标示
     */
    public static boolean isInRangeOrEquals(BigDecimal target, BigDecimal start, BigDecimal end) {
        if (target == null || start == null || end == null) {
            throw new RuntimeException("数值不能为空");
        }
        return target.compareTo(start) > 0 && target.compareTo(end) < 0;
    }

    /**
     * 判断数值是否在范围内
     *
     * @param target 判断值
     * @param start  范围开始
     * @param end    范围结束
     * @return 结果标示
     */
    public static boolean isInRangeOrEquals(String target, String start, String end) {
        if (StringUtils.isEmpty(target) || StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
            throw new RuntimeException("数值不能为空");
        }
        return isInRangeOrEquals(parseString(target), parseString(start), parseString(end));
    }

    /**
     * 判断数值是否在范围内
     *
     * @param target 判断值
     * @param start  范围开始
     * @param end    范围结束
     * @return 结果标示
     */
    public static boolean isInRange(BigDecimal target, BigDecimal start, BigDecimal end) {
        if (target == null || start == null || end == null) {
            throw new RuntimeException("数值不能为空");
        }
        return target.compareTo(start) >= 0 && target.compareTo(end) <= 0;
    }

    /**
     * 判断数值是否在范围内
     *
     * @param target 判断值
     * @param start  范围开始
     * @param end    范围结束
     * @return 结果标示
     */
    public static boolean isInRange(String target, String start, String end) {
        if (StringUtils.isEmpty(target) || StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
            throw new RuntimeException("数值不能为空");
        }
        return isInRange(parseString(target), parseString(start), parseString(end));
    }

    /**
     * string转Bigdecimal
     *
     * @param value string数值
     * @return 转换后数值
     */
    public static BigDecimal parseString(String value) {
        BigDecimal result = new BigDecimal(0);
        if (StringUtils.isNotEmpty(value)) {
            try {
                result = new BigDecimal(value.trim());
            } catch (Exception e) {
                throw new RuntimeException("传入数值不合法！");
            }
        }
        return result;
    }

    /**
     * Object转BigDecimal
     *
     * @param value 值
     */
    public static BigDecimal getBigDecimal(Object value) {
        BigDecimal ret = null;
        if (value != null) {
            if (value instanceof BigDecimal) {
                ret = (BigDecimal) value;
            } else if (value instanceof String) {
                if (isNumber((String) value)) {
                    ret = new BigDecimal((String) value);
                } else {
                    throw new ClassCastException("[" + value + "] is not a correct BigDecimal value.");
                }
            } else if (value instanceof BigInteger) {
                ret = new BigDecimal((BigInteger) value);
            } else if (value instanceof Number) {
                ret = new BigDecimal(String.valueOf(((Number) value).doubleValue()));
            } else {
                throw new ClassCastException("Not possible to coerce [" + value + "] from class " + value.getClass() + " into a BigDecimal.");
            }
        }
        return ret;
    }

    /**
     * 判断数值是否在区间范围内
     *
     * @param value            数值
     * @param lowerLimitValue  下限值
     * @param lowerLimitSymbol 下限符号
     * @param upperLimitValue  上限值
     * @param upperLimitSymbol 上限符号
     * @return true: 在区间范围内; false: 不在区间范围内; null: 不判断
     */
    public static Boolean isInInterval(String value,
                                       String lowerLimitValue,
                                       String lowerLimitSymbol,
                                       String upperLimitValue,
                                       String upperLimitSymbol) {
        String interval = parseAsInterval(lowerLimitValue, lowerLimitSymbol, upperLimitValue, upperLimitSymbol);
        return isInInterval(value, interval);
    }

    /**
     * 判断数值是否在区间范围内
     *
     * @param value    数值
     * @param interval 开闭区间
     * @return true: 在区间范围内; false: 不在区间范围内; null: 不判断
     */
    public static Boolean isInInterval(String value, String interval) {
        //数值为空，不进行判断
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        // 获取开闭区间的最小值和最大值
        String[] pairs = interval.split(",");
        List<String> rangeNumList = Arrays.stream(pairs).map(str -> str.replaceAll("[(|)\\[\\]]", "")
                .trim()).collect(Collectors.toList());
        BigDecimal minValue = "".equals(rangeNumList.get(0)) ? BigDecimal.valueOf(Long.MIN_VALUE) : new BigDecimal(rangeNumList.get(0));
        BigDecimal maxValue = "".equals(rangeNumList.get(1)) ? BigDecimal.valueOf(Long.MAX_VALUE) : new BigDecimal(rangeNumList.get(1));

        // 判定数值是否大于最小值
        boolean minMatched;
        int minCompareResult = new BigDecimal(value).compareTo(minValue);
        if (minCompareResult < 0) {
            minMatched = false;
        } else if (minCompareResult == 0) {
            minMatched = !pairs[0].startsWith("(");
        } else {
            minMatched = true;
        }

        boolean maxMatched;
        int maxCompareResult = new BigDecimal(value).compareTo(maxValue);
        if (maxCompareResult < 0) {
            maxMatched = true;
        } else if (maxCompareResult == 0) {
            maxMatched = pairs[0].endsWith("]");
        } else {
            maxMatched = false;
        }
        return minMatched && maxMatched;
    }


    /**
     * 获取限值的开闭区间格式
     *
     * @param lowerLimitValue  下限值
     * @param lowerLimitSymbol 下限符号
     * @param upperLimitValue  上限值
     * @param upperLimitSymbol 上限符号
     * @return 开闭区间
     */
    public static String parseAsInterval(String lowerLimitValue,
                                         String lowerLimitSymbol,
                                         String upperLimitValue,
                                         String upperLimitSymbol) {
        StringBuilder interval = new StringBuilder();
        if (StringUtils.isEmpty(lowerLimitSymbol)
                || LOWER_LIMIT_SYMBOL.get(0).equals(lowerLimitSymbol)
                || LOWER_LIMIT_SYMBOL.get(1).equals(lowerLimitSymbol)) {
            interval.append("[");
        } else {
            interval.append("(");
        }
        if (StringUtils.isNotEmpty(lowerLimitValue)) {
            interval.append(lowerLimitValue);
        }

        interval.append(",");

        if (StringUtils.isNotEmpty(upperLimitValue)) {
            interval.append(upperLimitValue);
        }

        if (StringUtils.isEmpty(upperLimitSymbol)
                || UPPER_LIMIT_SYMBOL.get(0).equals(lowerLimitSymbol)
                || UPPER_LIMIT_SYMBOL.get(1).equals(lowerLimitSymbol)) {
            interval.append("]");
        } else {
            interval.append(")");
        }
        return interval.toString();
    }

    /**
     * 判断表达式是否是合法的(正确的格式示例："[x] > 5" , "[x] >= 6 and [x] <= 10")
     *
     * @param expression 给定字符串
     * @return 校验是否通过
     */
    public static boolean isValidExpression(String expression) {
        if (StringUtils.isEmpty(expression)) {
            return true;
        }
        expression = expression.replace(" ", "");
        if (expression.contains("and")) {
            String[] arr = expression.split("and");
            return arr.length == 2 && isValidExpression(arr[0]) && isValidExpression(arr[1]);
        } else {
            if (expression.startsWith("[x]") && countSpecialString(expression, "[x]") == 1) {
                expression = expression.replace("[x]", "");
                if (expression.startsWith("<=") || expression.startsWith(">=")) {
                    expression = expression.substring(2);
                } else if (expression.startsWith("<") || expression.startsWith(">")) {
                    expression = expression.substring(1);
                }
                return isNumber(expression);
            }
        }
        return false;
    }

    /**
     * 统计给定的子串subStr在主串str中出现的次数
     *
     * @param str    主串
     * @param subStr 子串
     * @return 子串subStr在主串str中出现的次数
     */
    public static int countSpecialString(String str, String subStr) {
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(subStr)) {
            return 0;
        }
        int idx = str.indexOf(subStr);
        return idx == -1 ? 0 : 1 + countSpecialString(str.substring(idx + subStr.length()), subStr);
    }

    /**
     * 将整数数字转换成中文数字
     *
     * @param number 整数数字
     * @return 中文数字
     */
    public static String long2Chinese(long number) {
        String[] numbers = {"一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String[] unit = {" ", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千"};
        String numStr = String.valueOf(number);
        char[] chars = numStr.toCharArray();
        int length = chars.length;
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < chars.length; i++) {
            char ca = chars[i];
            int c = ca - '0';
            if (c != 0) {
                result.append(numbers[c - 1]).append(unit[length - i - 1]);
            }
        }
        return result.toString().trim();
    }

    /**
     * 移除字符串中小数点后的无效零。
     *
     * @param value 要处理的字符串值，该值可能包含小数部分。
     * @return 移除小数点后无效零后的字符串。如果输入字符串不包含小数点，则返回原字符串。
     */
    public static String removeInValidZero(String value) {
        if (value.contains(".")) {
            return value.replaceAll("\\.?0+$", "");
        } else {
            return value;
        }
    }

    /**
     * 计算给定值的以10为底的对数，并返回计算结果作为字符串。
     *
     * @param value 需要计算对数的值，类型为字符串。
     * @return 计算结果作为字符串返回。如果输入值为null或空字符串，则直接返回原值；如果输入值不是数字，则抛出IllegalArgumentException异常。
     * @throws IllegalArgumentException 如果输入值不是数字，则抛出此异常。
     */
    public static String calculateLg(String value) {
        if (value == null || value.isEmpty()) {
            return value;
        }
        if (!isNumber(value)) {
            throw new IllegalArgumentException("用于Lg计算的参数必须是数字");
        }
        double result = Math.log(Double.parseDouble(value)) / Math.log(10);
        return Double.toString(result);
    }


    /**
     * 计算标准方差
     *
     * @param values 原始数据
     * @return 标准方差
     */
    public static double calculateStandardDeviation(String... values) {
        //先计算这组数据的平均值
        String avg = calculateAvg(values);
        //计算这组数据中每个数与平均值的差的平方和
        List<String> squareDifferences = new ArrayList<>();
        for (String value : values) {
            String differenceStr = subtract(value, avg);
            double difference = Double.parseDouble(differenceStr);
            squareDifferences.add(Double.toString(difference * difference));
        }
        //计算上面平方差的和的平均值
        avg = calculateAvg(squareDifferences.toArray(new String[0]));
        //计算上面平均值的平方根
        return Math.sqrt(Double.parseDouble(avg));
    }

}
