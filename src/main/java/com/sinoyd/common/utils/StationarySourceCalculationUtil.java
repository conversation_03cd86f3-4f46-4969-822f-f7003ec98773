package com.sinoyd.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 嗅辨固定源计算工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/02/28
 **/
public class StationarySourceCalculationUtil {

    /**
     * 计算阈值
     *
     * @param maxCorrectDilutionRate 最大正确稀释率，字符串形式
     * @param wrongDilutionRate      错误稀释率，字符串形式
     * @param scale                  结果保留的小数位数
     * @return 阈值，以字符串形式返回
     */
    public static String calculateThreshold(String maxCorrectDilutionRate, String wrongDilutionRate, int scale) {
        String result = MathUtil.calculateAvg(MathUtil.calculateLg(maxCorrectDilutionRate), MathUtil.calculateLg(wrongDilutionRate));
        if (scale == -1) {
            return result;
        }
        return new BigDecimal(result).setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }

    /**
     * 计算样本标准差
     *
     * @param values 样本数据值数组，每个元素都是字符串形式的数字
     * @param scale  结果保留的小数位数
     * @return 计算得到的样本标准差，如果isRevision为true，则结果保留两位小数并进行四舍六入五成双修约；否则直接返回计算结果的字符串表示
     */
    public static String calculateSampleStandardDeviation(int scale, String... values) {
        //计算样本均值
        String avg = MathUtil.calculateAvg(values);
        //计算样本方差，采用贝塞尔校正
        double sum = 0.0;
        for (String value : values) {
            sum += Math.pow(Double.parseDouble(value) - Double.parseDouble(avg), 2);
        }
        double variance = sum / (values.length - 1);
        //计算样本标准差，并对结果进行修约
        BigDecimal result = BigDecimal.valueOf(Math.sqrt(variance));
        if (scale == -1) {
            return result.toPlainString();
        }
        return result.setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }

    /**
     * 计算相关系数
     *
     * @param x     数据集合x，代表第一组嗅阈值
     * @param y     数据集合y，代表第二组嗅阈值
     * @param scale 结果保留的小数位数
     * @return 皮尔逊相关系数
     */
    public static String calculateRelationParam(String[] x, String[] y, int scale) {
        if (x.length != y.length) {
            throw new IllegalArgumentException("两组数据的长度必须相同");
        }
        for (int i = 0; i < x.length; i++) {
            if (x[i].isEmpty() || y[i].isEmpty() || !MathUtil.isNumber(x[i]) || !MathUtil.isNumber(y[i])) {
                throw new IllegalArgumentException("所有数据必须是合法的数字");
            }
        }
        int n = x.length;
        double sumX = 0, sumY = 0;
        double sumXY = 0, sumXSq = 0, sumYSq = 0;

        // 计算均值和各项累加值
        for (int i = 0; i < n; i++) {
            double xVal = Double.parseDouble(x[i]);
            double yVal = Double.parseDouble(y[i]);
            sumX += xVal;
            sumY += yVal;
            sumXY += xVal * yVal;
            sumXSq += xVal * xVal;
            sumYSq += yVal * yVal;
        }
        // 计算均值
        double avgX = sumX / n;
        double avgY = sumY / n;
        // 计算分子和分母
        double numerator = sumXY - sumX * avgY - sumY * avgX + n * avgX * avgY;
        double denominator = Math.sqrt((sumXSq - sumX * avgX - sumX * avgX + n * avgX * avgX)
                * (sumYSq - sumY * avgY - sumY * avgY + n * avgY * avgY));

        // 避免除以零的情况
        if (denominator == 0) {
            return "0";
        }

        // 计算相关系数
        double result = numerator / denominator;
        if (scale == -1) {
            return String.valueOf(result);
        }
        return BigDecimal.valueOf(result).setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }

    /**
     * 计算检验统计量
     *
     * @param x     第一组嗅阈值数据集合
     * @param y     第二组嗅阈值数据集合
     * @param scale 结果保留的小数位数
     * @return 计算得到的检验统计量，以字符串形式返回
     */
    public static String calculateInspectionStatistic(String[] x, String[] y, int scale) {
        //阈值结果个数
        int n = x.length;
        //计算2个小组的阈值均值
        String xAvg = MathUtil.calculateAvg(x);
        String yAvg = MathUtil.calculateAvg(y);
        //相关系数
        String paramR = calculateRelationParam(x, y, 4);
        //计算2个小组的阈值标准差(样本方差)
        String xDeviation = calculateSampleStandardDeviation(4, x);
        String yDeviation = calculateSampleStandardDeviation(4, y);
        //计算分子
        String numerator = String.valueOf(Math.abs(Double.parseDouble(MathUtil.subtract(xAvg, yAvg))));
        //计算分母
        double xDeviationDouble = Double.parseDouble(xDeviation);
        double yDeviationDouble = Double.parseDouble(yDeviation);
        double denominator = xDeviationDouble * xDeviationDouble + yDeviationDouble * yDeviationDouble
                - (2 * Double.parseDouble(paramR) * xDeviationDouble * yDeviationDouble);
        denominator = Math.sqrt(denominator / (n - 1));
        String denominatorStr = String.valueOf(denominator);
        //防止分母为0
        if ("0".equals(denominatorStr) || "0.0".equals(denominatorStr)) {
            return "0";
        }
        //计算t值
        double tResult = Double.parseDouble(numerator) / denominator;
        if (scale == -1) {
            return String.valueOf(tResult);
        }
        return BigDecimal.valueOf(tResult).setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }

    /**
     * 计算两个阈值数组的平均值
     *
     * @param scale            保留的小数位数
     * @param xThresholdValues 第一组个人阈值数组
     * @param yThresholdValues 第二组个人阈值数组
     * @return 返回合并后数组的平均值，作为字符串表示
     */
    public static String calculateAvgThreshold(int scale, String[] xThresholdValues, String[] yThresholdValues) {
        String[] values = new String[xThresholdValues.length + yThresholdValues.length];
        System.arraycopy(xThresholdValues, 0, values, 0, xThresholdValues.length);
        System.arraycopy(yThresholdValues, 0, values, xThresholdValues.length, yThresholdValues.length);
        return calculateAvgThreshold(scale, values);
    }

    /**
     * 计算两个阈值数组的平均值
     *
     * @param scale           保留的小数位数
     * @param thresholdValues 个人阈值数组
     * @return 返回合并后数组的平均值，作为字符串表示
     */
    public static String calculateAvgThreshold(int scale, String... thresholdValues) {
        String avg = MathUtil.calculateAvg(thresholdValues);
        if (scale == -1) {
            return avg;
        }
        return BigDecimal.valueOf(Double.parseDouble(avg)).setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }

    /**
     * 计算臭气浓度
     *
     * @param scale           结果保留的小数位数
     * @param thresholdValues 个人阈值数组
     * @return 返回计算出的臭气浓度
     */
    public static String calculateStenchDensity(int scale, String... thresholdValues) {
        String avg = MathUtil.calculateAvg(thresholdValues);
        double result = Math.pow(10, Double.parseDouble(avg));
        if (scale == -1) {
            return String.valueOf(result);
        }
        return BigDecimal.valueOf(result).setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }

}
