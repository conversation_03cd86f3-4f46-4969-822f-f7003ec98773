package com.sinoyd.common.utils;

import com.aspose.pdf.Font;
import com.aspose.pdf.*;
import com.aspose.pdf.facades.PdfFileMend;
import com.sinoyd.common.vo.WaterMarkStyleVO;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.file.PathUtils;
import org.apache.commons.lang3.StringUtils;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/19
 */
public class FileUtil {

    /**
     * 图片类型
     */
    public static final List<String> IMAGE_TYPE_LIST = Stream.of("bmp", "jpeg", "gif", "psd", "png", "tiff", "jpg")
            .collect(Collectors.toList());

    /**
     * 文件分隔符
     */
    public static final String FILE_SEPARATOR = "/";

    /**
     * 时间戳格式
     */
    private static final String TIMESTAMP_FORMAT = "yyyyMMddHHmmssSSS";

    /**
     * 给文件名加上时间戳
     *
     * @param originFileName 原文件名
     * @return 加上时间戳的文件名
     */
    public static String loadFileNameWithTimestamp(String originFileName) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(TIMESTAMP_FORMAT);
        return dateFormat.format(new Date()) + "_" + originFileName;
    }

    /**
     * 下载文件
     *
     * @param path     下载路径
     * @param filename 文件名称
     * @param response 浏览器响应数据
     */
    public static void download(String path, String filename, HttpServletResponse response) {
        File file = new File(path);
        if (!file.exists()) {
            throw new RuntimeException("文件[" + path + "]不存在");
        }
        FileInputStream fileInputStream = null;
        OutputStream outputStream = null;
        try {
            fileInputStream = new FileInputStream(file);
            response.setHeader("Content-Disposition", "attachment;Filename=" + URLEncoder.encode(filename, "UTF-8"));
            outputStream = response.getOutputStream();

            byte[] bytes = new byte[2048];
            int len;
            while ((len = fileInputStream.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
        } catch (Exception e) {
            throw new RuntimeException("下载文件失败");
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 删除文件
     *
     * @param filePath 文件全路径
     */
    public static void delete(String filePath) {
        try {
            PathUtils.delete(Paths.get(filePath));
        } catch (IOException e) {
            throw new RuntimeException("删除文件失败");
        }
    }

    /**
     * 压缩指定目录
     *
     * @param directory        要压缩的路径
     * @param response         response流
     * @param keepDirStructure 是否保持原有目录结构
     */
    public static void zipDirectory(String directory, HttpServletResponse response, boolean keepDirStructure) {
        OutputStream os = null;
        ZipOutputStream zos = null;
        try {
            //压缩完成后压缩包放在response流中返回前端
            os = response.getOutputStream();
            zos = new ZipOutputStream(os);
            File sourceFile = new File(directory);
            compress(sourceFile, zos, sourceFile.getName(), keepDirStructure);
        } catch (IOException e) {
            throw new RuntimeException("压缩文件或目录失败");
        } finally {
            //完成压缩后删除临时文件夹
            delete(directory);
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 图片路径转成字符串（按大小压缩）
     *
     * @param rootPath     根路径
     * @param relativePath 源文件路径(相对路径)
     * @param width        宽度
     * @param height       高度
     * @param isResize     是否按指定大小重新绘制
     * @return 返回相应的字符串
     */
    public static String convertImage2Base64(String rootPath,
                                             String relativePath,
                                             String outRootPath,
                                             Integer width,
                                             Integer height,
                                             boolean isResize) {
        String content = "";
        if (StringUtils.isNotEmpty(relativePath) && relativePath.lastIndexOf(".") >= 0) {
            String fileName = relativePath.substring(relativePath.lastIndexOf(FILE_SEPARATOR) + 1);
            String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
            String fullPath = rootPath + relativePath;
            File file = new File(fullPath);
            if (file.exists()) {
                String newPath = fullPath;
                if (isResize) {
                    newPath = resizeImage(rootPath, relativePath, outRootPath, width, height);
                }
                return getImageContent(newPath, fileType);
            }
        } else {
            throw new RuntimeException("源文件路径不合法");
        }
        return content;
    }

    /**
     * 图片路径转成字符串（不压缩）
     *
     * @param relativePath 路径
     * @return 返回相应的字符串
     */
    public static String convertImage2Base64(String rootPath, String relativePath) {
        if (StringUtils.isNotEmpty(relativePath) && relativePath.lastIndexOf(".") >= 0) {
            String fileType = relativePath.substring(relativePath.lastIndexOf(".") + 1);
            //拼接物理地址
            String path = rootPath + FILE_SEPARATOR + relativePath;
            File file = new File(path);
            if (file.exists()) {
                return getImageContent(path, fileType);
            }
        }
        return "";
    }

    /**
     * 重新按指定大小绘制图片
     *
     * @param rootPath     根路径
     * @param relativePath 源文件路径，相对路径
     * @param outRootPath  输出根目录
     * @param w            宽度
     * @param h            高度
     * @return 重新绘制的图片路径
     */
    public static String resizeImage(String rootPath,
                                     String relativePath,
                                     String outRootPath,
                                     Integer w,
                                     Integer h) {
        //获取文件名称
        String fileName = relativePath.substring(relativePath.lastIndexOf(FILE_SEPARATOR) + 1);
        //判断输出目录是否存在，不存在则创建
        String fullOutFilePath = outRootPath + FILE_SEPARATOR + fileName;
        File outDirectory = new File(fullOutFilePath).getParentFile();
        if (!outDirectory.exists()) {
            outDirectory.mkdirs();
        }
        //本身文件路径
        String fullFilePath = rootPath + FILE_SEPARATOR + relativePath;
        File file = new File(fullFilePath);
        FileOutputStream out = null;
        try {
            Image image = ImageIO.read(file);
            BufferedImage img = new BufferedImage(w, h, BufferedImage.TYPE_INT_RGB);
            img.getGraphics().drawImage(image, 0, 0, w, h, null);
            File desFile = new File(fullOutFilePath);
            out = new FileOutputStream(desFile);
            // 可以正常实现bmp、png、gif转jpg
            ImageIO.write(img, "jpeg", out);
        } catch (IOException e) {
            throw new RuntimeException("重新按指定大小绘制图片发生错误");
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return fullOutFilePath;
    }

    /**
     * 根据图片地址转换为base64编码字符串
     *
     * @param imgFile  图片文件
     * @param fileType 图片类型
     * @return 返回base64相关内容
     */
    private static String getImageContent(String imgFile, String fileType) {
        if (!IMAGE_TYPE_LIST.contains(fileType.toLowerCase())) {
            throw new RuntimeException("不支持的图片类型，目前系统支持的图片类型有" + IMAGE_TYPE_LIST);
        }
        String imgStr;
        InputStream inputStream = null;
        try {
            inputStream = Files.newInputStream(Paths.get(imgFile));
            byte[] data = new byte[inputStream.available()];
            inputStream.read(data);
            // 加密
            BASE64Encoder encoder = new BASE64Encoder();
            imgStr = encoder.encode(data);
        } catch (IOException e) {
            throw new RuntimeException("转换图片失败");
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        if (StringUtils.isNotEmpty(imgStr)) {
            return "data:image/jpeg;base64," + imgStr;
        } else {
            throw new RuntimeException("图片内容为空");
        }
    }

    /**
     * 递归压缩
     * <p>
     * 可选是否保持目录结构，如果不保持则所有子路径中的文件会压缩到同一个文件夹中，
     * 注意: 如果子路径中的文件名相同，则会进行覆盖操作。
     * </p>
     *
     * @param sourceFile       压缩文件路径
     * @param zos              zip输出流
     * @param zipFileName      压缩后的zip文件名
     * @param keepDirStructure 是否保持目录结构
     */
    public static void compress(File sourceFile, ZipOutputStream zos, String zipFileName, boolean keepDirStructure) {
        byte[] buf = new byte[2048];
        InputStream is = null;
        try {
            if (sourceFile.isFile()) {
                //如果需要压缩的文件不是目录， 是文件
                zos.putNextEntry(new ZipEntry(zipFileName));
                int len;
                is = Files.newInputStream(sourceFile.toPath());
                while ((len = is.read(buf)) != -1) {
                    zos.write(buf, 0, len);
                }
                zos.closeEntry();
            } else {
                //如果需要压缩的文件是目录
                File[] listFiles = sourceFile.listFiles();
                if (listFiles == null || listFiles.length == 0) {
                    // 需要保留原来的文件结构时,需要对空文件夹进行处理
                    if (keepDirStructure) {
                        // 空文件夹的处理
                        zos.putNextEntry(new ZipEntry(zipFileName + FILE_SEPARATOR));
                        // 没有文件，不需要文件的copy
                        zos.closeEntry();
                    }
                } else {
                    for (File file : listFiles) {
                        // 判断是否需要保留原来的文件结构
                        if (keepDirStructure) {
                            // 注意：file.getName()前面需要带上父文件夹的名字加一斜杠,
                            // 不然最后压缩包中就不能保留原来的文件结构,即：所有文件都跑到压缩包根目录下了
                            compress(file, zos, zipFileName + FILE_SEPARATOR + file.getName(), true);
                        } else {
                            compress(file, zos, file.getName(), false);
                        }
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("压缩文件发生错误");
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 将BufferedImage转换成base64形式
     *
     * @param bufferedImage 图像实例
     * @return 图像base64形式
     */
    public static String bufferImage2Base64(BufferedImage bufferedImage) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            ImageIO.write(bufferedImage, "png", baos);
            byte[] bytes = baos.toByteArray();
            BASE64Encoder encoder = new BASE64Encoder();
            String base64Image = encoder.encodeBuffer(bytes).trim();
            base64Image = base64Image.replaceAll("\n", "").replaceAll("\r", "");
            return "data:image/jpg;base64," + base64Image;
        } catch (Exception e) {
            throw new RuntimeException("图像转换BASE64失败");
        }
    }

    /**
     * 关闭流
     *
     * @param c 流
     */
    public static void close(Closeable c) {
        try {
            if (c != null) {
                c.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 复制文件
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     */
    public static void copyFile(File sourceFile, File targetFile) {
        try {
            FileUtils.copyFile(sourceFile, targetFile);
        } catch (Exception e) {
            throw new RuntimeException("复制文件失败!");
        }
    }

    /**
     * pdf添加水印
     *
     * @param filePath 文件地址
     * @param styleVO  水印样式
     */
    public static void watermark(String filePath, WaterMarkStyleVO styleVO) {
        try {
            if (!AsposeLicenseUtil.isPdfLicense()) {
                throw new RuntimeException("验证远大文件体系PDF证书失败");
            }
            Document pdfDocument = new Document(filePath);
            // 创建水印
            for (Page page : pdfDocument.getPages()) {
                // 设置字体（依次尝试楷体、宋体、黑体）
                Font font;
                try {
                    font = FontRepository.findFont("KaiTi");
                } catch (Exception e) {
                    try {
                        font = FontRepository.findFont("SimSun");
                    } catch (Exception e2) {
                        font = FontRepository.findFont("SimHei");
                    }
                }
                // 分割水印内容为两行
                String[] lines = styleVO.getMarkContent().split("；");
                if (lines.length >= 2) {
                    float fontSize = styleVO.getTextSize();
                    // 创建第一行水印
                    TextStamp firstLineStamp = new TextStamp(lines[0]);
                    //计算比例
                    float zoom = (float) (page.getRect().getWidth() / page.getPageInfo().getWidth());
                    //计算字体大小 100% = 50F
                    fontSize = fontSize * zoom;

                    float xff = ((float) (page.getRect().getWidth() * styleVO.getWidth())) - fontSize * styleVO.getFirstSize();
                    System.out.println("...... 当前页的宽度：" + page.getRect().getWidth() + " ......... ");
                    System.out.println("...... 第一个水印字体的偏移量：" + fontSize * styleVO.getFirstSize() + " ......... ");
                    System.out.println("...... 第一个水印偏转的位置：" + xff + " ......... ");
                    setupTextStamp(firstLineStamp, styleVO, font, xff, 0, fontSize);

                    // 创建第二行水印
                    TextStamp secondLineStamp = new TextStamp(lines[1]);
                    xff = ((float) (page.getRect().getWidth() * styleVO.getWidth())) - fontSize * styleVO.getSecondSize();
                    System.out.println("...... 第二个水印字体的偏移量：" + fontSize * styleVO.getSecondSize() + " ......... ");
                    System.out.println("...... 第二个水印偏转的位置：" + xff + " ......... ");
                    setupTextStamp(secondLineStamp, styleVO, font, xff, 0, fontSize);

                    // 添加两行水印
                    page.addStamp(firstLineStamp);
                    page.addStamp(secondLineStamp);
                } else {
                    // 如果只有一行，则创建单行水印
                    TextStamp textStamp = new TextStamp(styleVO.getMarkContent());
                    setupTextStamp(textStamp, styleVO, font, 0, 0, styleVO.getTextSize());
                    page.addStamp(textStamp);
                }
            }

            pdfDocument.save(filePath);
        } catch (Exception ex) {
            throw new RuntimeException("添加PDF水印失败: " + ex.getMessage());
        }
    }

    /**
     * 设置水印样式
     *
     * @param textStamp 水印对象
     * @param styleVO   样式配置
     * @param font      字体
     * @param xOffset   X轴偏移量
     * @param yOffset   Y轴偏移量
     * @param textSize  字体大小
     */
    private static void setupTextStamp(TextStamp textStamp, WaterMarkStyleVO styleVO, com.aspose.pdf.Font font, float xOffset, float yOffset, float textSize) {
        // 设置水印位置为居中
//        textStamp.setHorizontalAlignment(styleVO.getArtifactHorizontal());
        textStamp.setVerticalAlignment(styleVO.getArtifactVertical());
        textStamp.setXIndent(xOffset);
        textStamp.setYIndent(yOffset);
        // 设置旋转角度
        textStamp.setRotateAngle(styleVO.getRotation());
        // 设置透明度
        textStamp.setOpacity(styleVO.getOpacity());
        // 设置字体和大小
        textStamp.getTextState().setFont(font);
        textStamp.getTextState().setFontSize(textSize);
        // 设置颜色
        com.aspose.pdf.Color watermarkColor = com.aspose.pdf.Color.fromRgb(styleVO.getColor());
        textStamp.getTextState().setForegroundColor(watermarkColor);
        textStamp.getTextState().setStrokingColor(watermarkColor);
        // 不需要描边效果
        textStamp.getTextState().setRenderingMode(TextRenderingMode.FillText);
    }

    /**
     * pdf设置状态图片
     *
     * @param filePath    文件地址
     * @param inputStream 图片文件流
     */
    public static void statusPic(String filePath, InputStream inputStream) {
        try {
            if (!AsposeLicenseUtil.isPdfLicense()) {
                throw new RuntimeException("验证远大文件体系PDF证书失败");
            }
            Document pdfDocument = new Document(filePath);
            Page page = pdfDocument.getPages().get_Item(1);
            double height = page.getRect().getHeight();
            pdfDocument.close();
            PdfFileMend mender = new PdfFileMend();
            mender.bindPdf(filePath);
            //页面纸张大小不是相同的，导致要动态计算
            mender.addImage(inputStream, 1, 10, (float) (height - 50), 90, (float) height);
            mender.save(filePath);
            mender.close();
        } catch (Exception ex) {
            throw new RuntimeException("设置文件状态标识失败: " + ex.getMessage());
        }
    }

    /**
     * 创建文件以及文件夹
     *
     * @param filePath 文件路径
     */
    public static void createFile(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            // 创建父目录（如果不存在）
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                if (!parentDir.mkdirs()) {
                    throw new RuntimeException("创建文件路径失败: " + parentDir.getAbsolutePath());
                }
            }
            try {
                file.createNewFile();
            } catch (IOException e) {
                throw new RuntimeException("创建文件失败: " + filePath, e);
            }
        }
    }
}