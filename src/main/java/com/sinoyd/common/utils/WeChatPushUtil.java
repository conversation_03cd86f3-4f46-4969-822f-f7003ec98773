package com.sinoyd.common.utils;

import com.sinoyd.common.http.HTTPCaller;
import com.sinoyd.common.vo.WeChatPushParamVO;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

/**
 * 微信推送工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/24
 **/
public class WeChatPushUtil {

    /**
     * 向微信服务器推送消息。
     *
     * @param paramVO 包含推送参数的VO对象，包括URL、消息内容等。
     * @throws RuntimeException 如果推送过程中出现错误，将抛出运行时异常。
     */
    public static String push(WeChatPushParamVO paramVO) {
        long random = 0;
        while (random < 3000) {
            random = (long) (Math.random() * 10000);
        }
        try {
            Thread.sleep(random);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        Request.Builder requestBuilder = loadRequestBuilder(paramVO);
        RequestBody requestBody = RequestBody.create(generateRequestBody(paramVO), MediaType.parse("application/json;charset=utf-8"));
        Request request = requestBuilder.url(paramVO.getUrl()).post(requestBody).build();
        try (Response response = HTTPCaller.getInstance(true).getOkHttpClient().newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new RuntimeException(String.format("...... 微信推送发生错误: [response code: %s, request method : %s, request url: %s, error message : %s] ......",
                        response.code(), "POST", paramVO.getUrl(), response.body() != null ? response.body().string() : ""));
            }
            return response.body() == null ? "推送发生错误" : response.body().string();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据WeChatPushParamVO对象生成请求体JSON字符串。
     *
     * @param paramVO 包含推送参数的VO对象
     * @return 生成的请求体JSON字符串
     */
    private static String generateRequestBody(WeChatPushParamVO paramVO) {
        String json = "{\n" +
                "\t\"targetKey\": \"targetValue\",\n" +
                "\t\"messageKey\": \"messageValue\"\n" +
                "}";
        if (StringUtils.isNotEmpty(paramVO.getRoomName())) {
            json = json.replace("targetKey", "room_name");
            json = json.replace("targetValue", paramVO.getRoomName());
        } else if (StringUtils.isNotEmpty(paramVO.getRoomId())) {
            json = json.replace("targetKey", "room_id");
            json = json.replace("targetValue", paramVO.getRoomId());
        } else if (StringUtils.isNotEmpty(paramVO.getUserName())) {
            json = json.replace("targetKey", "user_name");
            json = json.replace("targetValue", paramVO.getUserName());
        } else {
            json = json.replace("targetKey", "user_id");
            json = json.replace("targetValue", paramVO.getUserId());
        }
        json = json.replace("messageKey", "message");
        json = json.replace("messageValue", paramVO.getMessage());
        return json;
    }

    /**
     * 加载请求构建器
     *
     * @return 请求构建器实例
     */
    private static Request.Builder loadRequestBuilder(WeChatPushParamVO paramVO) {
        Request.Builder requestBuilder = new Request.Builder();
        requestBuilder.addHeader("ddc-token", generateDdcToken(paramVO));
        return requestBuilder;
    }

    /**
     * 生成DDC令牌
     *
     * @param paramVO 微信推送参数对象
     * @return 生成的DDC令牌
     */
    private static String generateDdcToken(WeChatPushParamVO paramVO) {
        String a = paramVO.getAppId() + "-" + System.currentTimeMillis() / 1000;
        String b = "IiI=";
        String c = MD5Util.encode(a + "." + b + "." + paramVO.getAppSecret());
        return a + "." + b + "." + c;
    }

}
