package com.sinoyd.common.utils;


import com.alibaba.fastjson.JSONObject;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.StringReader;
import java.util.*;

/**
 * Xml工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/02/20
 **/
public class XmlUtil {

    /**
     * 将XML字符串转换为JSON对象
     *
     * @param xml XML字符串
     * @return 转换后的JSON对象
     *
     * <p>该方法首先将XML字符串通过xml2Map方法转换为Map对象，
     * 然后将Map对象转换为JSON字符串，最后将JSON字符串解析为JSON对象并返回。</p>
     */
    public static JSONObject xml2Json(String xml) {
        Map<String, Object> map = xml2Map(xml);
        return JSONObject.parseObject(JSONObject.toJSONString(map));
    }

    /**
     * 将XML字符串转换为Map对象
     *
     * @param xmlString XML字符串
     * @return 包含XML内容的Map对象，其中键为XML元素的名称，值为对应的元素内容或嵌套Map
     * @throws RuntimeException 如果在解析XML字符串时发生异常，将抛出此异常
     *
     *                          <p>该方法将传入的XML字符串解析为DOM文档，并将文档的根元素及其子元素递归地转换为Map对象。
     *                          如果元素包含子元素，则调用element2Map方法将这些子元素转换为嵌套的Map；
     *                          如果元素不包含子元素，则直接将元素的文本内容作为值添加到Map中。
     *                          如果解析过程中出现任何异常，将捕获该异常并抛出一个RuntimeException。</p>
     */
    private static Map<String, Object> xml2Map(String xmlString) {
        try {
            StringReader stringReader = new StringReader(xmlString);
            SAXReader reader = new SAXReader();
            Document doc = reader.read(stringReader);
            Map<String, Object> map = new HashMap<String, Object>();
            if (doc == null)
                return map;
            Element root = doc.getRootElement();
            for (Iterator<Element> iterator = root.elementIterator(); iterator.hasNext(); ) {
                Element e = iterator.next();
                List<Element> list = e.elements();
                if (!list.isEmpty()) {
                    map.put(e.getName(), element2Map(e));
                } else
                    map.put(e.getName(), e.getText());
            }
            return map;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将XML元素转换为Map对象
     *
     * @param e XML元素对象
     * @return 包含元素名称和对应值的Map对象
     * @throws ClassCastException 如果元素类型不匹配将抛出类型转换异常
     *
     *                            <p>该方法递归地将XML元素转换为Map对象。如果元素包含子元素，则这些子元素会被转换为Map并添加到父Map中。
     *                            如果多个子元素具有相同的名称，则它们的值将被收集到一个列表中。如果元素没有子元素，则直接将元素的文本内容作为值添加到Map中。</p>
     */
    @SuppressWarnings("unchecked")
    private static Map<String, Object> element2Map(Element e) {
        Map<String, Object> map = new HashMap<>();
        List<?> list = e.elements();
        if (!list.isEmpty()) {
            for (Object object : list) {
                Element iter = (Element) object;
                List<Object> mapList = new ArrayList<>();
                if (!iter.elements().isEmpty()) {
                    Map<String, Object> m = element2Map(iter);
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (obj instanceof List) {
                            mapList = (List<Object>) obj;
                            mapList.add(m);
                        } else {
                            mapList = new ArrayList<>();
                            mapList.add(obj);
                            mapList.add(m);
                        }
                        map.put(iter.getName(), mapList);
                    } else
                        map.put(iter.getName(), m);
                } else {
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (obj instanceof List) {
                            mapList = (List<Object>) obj;
                            mapList.add(iter.getText());
                        } else {
                            mapList = new ArrayList<>();
                            mapList.add(obj);
                            mapList.add(iter.getText());
                        }
                        map.put(iter.getName(), mapList);
                    } else
                        map.put(iter.getName(), iter.getText());
                }
            }
        } else
            map.put(e.getName(), e.getText());
        return map;
    }
}
