package com.sinoyd.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 修约工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/19
 */
public class ReviseUtil {

    /**
     * 转换科学计数法
     *
     * @param value 原始数据
     * @return 原始数据的科学计数法的形式
     */
    public static String toScientificNotation(String value) {
        String format = "0.################E0";
        DecimalFormat df = new DecimalFormat(format);
        String result = df.format(new BigDecimal(value).doubleValue());
        if(result.contains("E") && !result.contains("E-")){
            return result.replace("E", "E+");
        }
        return result;
    }

    /**
     * 计算初始值的有效位
     *
     * @param value 初始值
     * @return 有效位
     */
    public static int calculateValueSign(String value) {
        value = value.replace(".", "")
                .replace("-", "");
        Pattern p = Pattern.compile("[^0]");
        Matcher m = p.matcher(value);
        if (m.find()) {
            int idx = m.start();
            return value.substring(idx).length();
        }
        return 0;
    }

    /**
     * 计算小数点后0的个数
     *
     * @param value 原始数据
     * @return 结果
     */
    public static int calculateZeroCountAfterPoint(String value) {
        int count = 0;
        if (value.startsWith("0.") || value.startsWith("-0.")) {
            value = value.split("\\.")[1];
            int valueSign = calculateValueSign(value);
            return value.length() - valueSign;
        }
        return count;
    }

    /**
     * 根据小数位进行修约
     *
     * @param value 原始数据
     * @param scale 小数位
     * @return 结果
     */
    public static String revise(String value, Integer scale) {
        //如果精度为空或者-1，无需修约直接返回原始数据
        if (scale == null || scale == -1) {
            return value;
        }
        BigDecimal bd = new BigDecimal(value).setScale(scale, RoundingMode.HALF_EVEN);
        return bd.toPlainString();
    }
}