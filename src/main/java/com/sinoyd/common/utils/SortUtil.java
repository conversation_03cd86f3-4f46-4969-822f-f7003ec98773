package com.sinoyd.common.utils;

import java.text.Collator;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 排序工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/24
 */
public class SortUtil {

    /**
     * 获取支持中文且按中文首字母的排序器
     *
     * @return 排序器
     */
    public static Comparator<Object> getCNComparator() {
        return Collator.getInstance(Locale.CHINA);
    }

    /**
     * 按数字优先的规则比较两个字符串
     * 排序规则：
     * 1. 如果字符串以数字开头，则优先按开头的数字排序
     * 2. 如果字符串中存在数字，则提取出其中数字，后续排序先按文本部分分组，再按数字顺序进行排序
     * 3. 如果字符中存在中文，按中文首字母排序
     * 4. 如果比较的字符串中不存在数字，则直接按字符进行排序
     * 5. 包含数字的字符串优先于不包含数字的字符串
     * 6. 提取数字后剩余部分内容相同的字符串为一组，组内按数字排序，组间按文本部分排序
     * 示例：字符串 "A1-2-3" 和 "A1-2-10" 属于同一组（文本部分都是"A--"），组内按数字[1,2,3]和[1,2,10]排序
     *
     * @param s1 字符串1
     * @param s2 字符串2
     * @return 比较结果：负数表示s1<s2，0表示s1=s2，正数表示s1>s2
     */
    public static int compareString(String s1, String s2) {
        if (s1 == null && s2 == null) {
            return 0;
        }
        if (s1 == null) {
            return -1;
        }
        if (s2 == null) {
            return 1;
        }

        // 检查是否以数字开头
        boolean startsWithNumber1 = startsWithNumber(s1);
        boolean startsWithNumber2 = startsWithNumber(s2);

        // 如果都以数字开头，优先按开头数字排序
        if (startsWithNumber1 && startsWithNumber2) {
            Long leadingNumber1 = extractLeadingNumber(s1);
            Long leadingNumber2 = extractLeadingNumber(s2);
            int leadingNumberCompare = leadingNumber1.compareTo(leadingNumber2);
            if (leadingNumberCompare != 0) {
                return leadingNumberCompare;
            }
            // 开头数字相同，继续按原有规则比较
        }

        // 如果一个以数字开头一个不是，以数字开头的排在前面
        if (startsWithNumber1 && !startsWithNumber2) {
            return -1;
        }
        if (!startsWithNumber1 && startsWithNumber2) {
            return 1;
        }

        // 提取两个字符串中的数字和非数字部分
        NumberStringParts parts1 = extractNumbersAndText(s1);
        NumberStringParts parts2 = extractNumbersAndText(s2);

        // 判断是否包含数字
        boolean hasNumbers1 = !parts1.numbers.isEmpty();
        boolean hasNumbers2 = !parts2.numbers.isEmpty();

        // 如果一个有数字一个没有，有数字的排在前面
        if (hasNumbers1 && !hasNumbers2) {
            return -1;
        }
        if (!hasNumbers1 && hasNumbers2) {
            return 1;
        }

        // 先比较文本部分（支持中文），确定分组
        int textCompareResult = getCNComparator().compare(parts1.text, parts2.text);
        if (textCompareResult != 0) {
            return textCompareResult;
        }

        // 文本部分相同，再比较数字部分
        if (hasNumbers1) {
            return compareNumbers(parts1.numbers, parts2.numbers);
        }

        // 都没有数字且文本相同，则相等
        return 0;
    }

    /**
     * 编号排序，针对编号以数字进行分割，后按照分割元素依次排序，
     * 比如样品编号 240101TR003， 先按照编号中数值进行正则拆分，拆分后为数组 [240101,TR,003]，
     * 再对数组中元素进行排序，这里要注意的是如果数组中的元素是数值，则把数值转成整型排序
     *
     * @param businessCodes 需要排序的编号
     * @return 排序后的编号
     */
    public static List<String> compareBusinessCode(Collection<String> businessCodes) {
        // 创建一个自定义的Comparator对象
        Comparator<String> comparator = (o1, o2) -> {
            // 拆分编号成数组
            List<String> s1_List = splitString(o1);
            List<String> s2_List = splitString(o2);

            // 按照数组元素进行比较
            int size = Math.min(s1_List.size(), s2_List.size());
            for (int i = 0; i < size; i++) {
                String s1 = s1_List.get(i);
                String s2 = s2_List.get(i);
                // 如果元素是纯数字，则按照数字大小比较
                int result = MathUtil.isNumber(s1) && MathUtil.isNumber(s2)
                        ? Long.valueOf(s1).compareTo(Long.valueOf(s2))
                        : s1.compareTo(s2);
                if (result != 0) {
                    return result;
                }
            }
            // 如果前面的元素都相同，则根据数组长度比较
            return Long.compare(s1_List.size(), s2_List.size());
        };
        // 排序
        List<String> resultList = new ArrayList<>(businessCodes);
        resultList.sort(comparator);
        return resultList;
    }


    /**
     * 获取排序值Map
     * key:编号
     * value:排序值
     *
     * @param businessCodes 需要排序的编号
     * @return 排序值映射Map
     */
    public static Map<String, Integer> getCompareSortNumMap(Collection<String> businessCodes) {
        Map<String, Integer> sortMap = new HashMap<>();
        List<String> sortCodes = compareBusinessCode(businessCodes);
        for (int i = 0; i < sortCodes.size(); i++) {
            sortMap.put(sortCodes.get(i), i);
        }
        return sortMap;
    }

    /**
     * 拆分字符串
     * 输入：第5章第100节课
     * 返回：[第,5,章第,100,节课]
     *
     * @param str 源字符串
     * @return 拆分后的字符串数组
     */
    private static List<String> splitString(String str) {
        Matcher matcher = Pattern.compile("([^0-9]+)|(\\d+)").matcher(str);
        List<String> stringList = new ArrayList<>();
        while (matcher.find()) {
            stringList.add(matcher.group());
        }
        return stringList;
    }

    /**
     * 提取字符串中的数字和文本部分
     *
     * @param str 源字符串
     * @return 包含数字列表和文本的对象
     */
    private static NumberStringParts extractNumbersAndText(String str) {
        List<Long> numbers = new ArrayList<>();
        StringBuilder textBuilder = new StringBuilder();

        Matcher matcher = Pattern.compile("(\\d+)|([^\\d]+)").matcher(str);
        while (matcher.find()) {
            String group = matcher.group();
            if (MathUtil.isNumber(group)) {
                numbers.add(Long.valueOf(group));
            } else {
                textBuilder.append(group);
            }
        }

        return new NumberStringParts(numbers, textBuilder.toString());
    }

    /**
     * 比较两个数字列表
     *
     * @param numbers1 数字列表1
     * @param numbers2 数字列表2
     * @return 比较结果
     */
    private static int compareNumbers(List<Long> numbers1, List<Long> numbers2) {
        int minSize = Math.min(numbers1.size(), numbers2.size());

        // 逐个比较数字
        for (int i = 0; i < minSize; i++) {
            int result = numbers1.get(i).compareTo(numbers2.get(i));
            if (result != 0) {
                return result;
            }
        }

        // 如果前面的数字都相同，比较数字个数
        return Long.compare(numbers1.size(), numbers2.size());
    }

    /**
     * 判断字符串是否以数字开头
     *
     * @param str 待检查的字符串
     * @return 如果以数字开头返回true，否则返回false
     */
    private static boolean startsWithNumber(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return Character.isDigit(str.charAt(0));
    }

    /**
     * 提取字符串开头的数字
     *
     * @param str 待提取的字符串
     * @return 开头的数字，如果不以数字开头则返回0
     */
    private static Long extractLeadingNumber(String str) {
        if (str == null || str.isEmpty() || !Character.isDigit(str.charAt(0))) {
            return 0L;
        }

        StringBuilder numberBuilder = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (Character.isDigit(c)) {
                numberBuilder.append(c);
            } else {
                break;
            }
        }

        try {
            return Long.valueOf(numberBuilder.toString());
        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    /**
     * 内部类：用于存储数字和文本部分
     */
    private static class NumberStringParts {
        final List<Long> numbers;
        final String text;

        NumberStringParts(List<Long> numbers, String text) {
            this.numbers = numbers;
            this.text = text;
        }
    }
}