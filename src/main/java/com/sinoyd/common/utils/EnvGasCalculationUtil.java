package com.sinoyd.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 环境空气计算工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/05
 **/
public class EnvGasCalculationUtil {

    /**
     * 计算小组平均正解率(M值)
     *
     * @param a     答案正确的人次数
     * @param b     答案不明的人次数
     * @param c     答案错误的人次数
     * @param scale 修约位数
     * @return 计算结果
     */
    public static String calculateMValue(int a, int b, int c, int scale) {
        double mValue = (a * 1.00 + b * 0.33 + c * 0.00) / 18;
        if (scale == -1) {
            return String.valueOf(mValue);
        }
        return new BigDecimal(mValue).setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }

    /**
     * 判断是否通过M值检验
     * 算法描述：
     * 1. 如果M1的值小于等于0.58，则直接通过检验，返回true。
     * 2. 如果M1的值大于0.58，则检查M2的值：
     * - 如果M2的值未提供（即m2Value为null或空字符串），则不通过检验，返回false。
     * - 如果M2的值小于0.58，则通过检验，返回true。
     * - 如果M2的值大于等于0.58，则不通过检验，返回false。
     *
     * @param m1Value M1的值，字符串形式
     * @param m2Value M2的值，字符串形式
     * @return 如果通过M值检验返回true，否则返回false
     */
    public static boolean calculateMPassed(String m1Value, String m2Value) {
        BigDecimal m1 = new BigDecimal(m1Value);
        //M1<=0.58，通过
        if (m1.compareTo(new BigDecimal("0.58")) <= 0) {
            return true;
        } else {
            //M1>0.58, M2未开始，不通过
            if (m2Value == null || m2Value.isEmpty()) {
                return false;
            } else {
                BigDecimal m2 = new BigDecimal(m2Value);
                //m2 < 0.58, 通过; m2 >= 0.58, 不通过
                return m2.compareTo(new BigDecimal("0.58")) < 0;
            }
        }
    }

    /**
     * 计算a参数的值
     * 算法描述：
     * 1. 将输入的m1Value和m2Value字符串转换为double类型的数值m1和m2。
     * 2. 根据公式 (m1 - 0.58) / (m1 - m2) 计算结果result。
     * 3. 如果scale参数为-1，则直接将result转换为字符串并返回。
     * 4. 否则，使用BigDecimal将result四舍五入到指定的小数位数，并转换为不带科学计数法的字符串形式返回。
     *
     * @param m1Value M1的值，字符串形式
     * @param m2Value M2的值，字符串形式
     * @param scale   结果保留的小数位数，-1表示不保留小数
     */
    public static String calculateAParam(String m1Value, String m2Value, int scale) {
        double m1 = Double.parseDouble(m1Value);
        double m2 = Double.parseDouble(m2Value);
        double result = (m1 - 0.58) / (m1 - m2);
        if (scale == -1) {
            return String.valueOf(result);
        }
        return new BigDecimal(result).setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }


    /**
     * 计算b参数的值
     *
     * @param t1    M1的稀释倍数
     * @param t2    M2的稀释倍数
     * @param scale 结果保留的小数位数，-1表示不保留小数
     * @return 返回计算得到的参数B的值，字符串形式
     */
    public static String calculateBParam(int t1, int t2, int scale) {
        double result = Math.log10((double) t2 / t1);
        if (scale == -1) {
            return String.valueOf(result);
        }
        return new BigDecimal(result).setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }

    /**
     * 计算臭气浓度
     *
     * @param t1     M1的稀释倍数
     * @param aValue 参数a的值，字符串形式
     * @param bValue 参数b的值，字符串形式
     * @param scale  结果保留的小数位数，-1表示不保留小数
     * @return 返回计算得到的臭气浓度值，字符串形式
     */
    public static String calculateStenchDensity(int t1, String aValue, String bValue, int scale) {
        double a = Double.parseDouble(aValue);
        double b = Double.parseDouble(bValue);
        double result = t1 * Math.pow(10, a * b);
        if (scale == -1) {
            return String.valueOf(result);
        }
        return new BigDecimal(result).setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }
}
