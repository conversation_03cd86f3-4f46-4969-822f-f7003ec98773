package com.sinoyd.common.utils;

import com.sinoyd.common.algorithm.comparison.enums.EnumOperationSymbol;
import com.sinoyd.common.http.HTTPCaller;
import com.sinoyd.common.http.NameValuePair;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 计算相关工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/20
 */
public class CalculationUtil {

    /**
     * 公式计算
     *
     * @param gateUrl         网关地址，比如 192.168.11.64:5200
     * @param token           身份令牌
     * @param formula         公式
     * @param params          参数集合
     * @param ignoreException 是否忽略异常，如果为true，在计算过程中发生错误则忽略且返回空字符结果
     * @return 计算结果
     */
    public static String calculationExpression(String gateUrl,
                                               String token,
                                               String formula,
                                               Map<String, Object> params,
                                               boolean ignoreException) {
        return calculationExpression(gateUrl, token, Collections.singletonList(formula), params, ignoreException);
    }


    /**
     * 公式计算
     *
     * @param gateUrl         网关地址，比如 192.168.11.64:5200
     * @param token           身份令牌
     * @param formulas        公式集合
     * @param params          参数集合
     * @param ignoreException 是否忽略异常，如果为true，在计算过程中发生错误则忽略且返回空字符结果
     * @return 计算结果
     */
    public static String calculationExpression(String gateUrl,
                                               String token,
                                               List<String> formulas,
                                               Map<String, Object> params,
                                               boolean ignoreException) {

        try {
            formulas = parseFormulas(formulas);
            List<NameValuePair> paramList = new ArrayList<>();
            paramList.add(new NameValuePair("exp", String.join(",", formulas)));
            paramList.add(new NameValuePair("params", params));
            return HTTPCaller.getInstance().postAsString(gateUrl,
                    "/api/proxy/tool/calculate",
                    token,
                    paramList);
        } catch (Exception e) {
            if (ignoreException) {
                return "";
            }
            String errorMsg = "计算时发生错误，请确认公式和参数是否正确，详情参考: " +
                    "[计算公式: " + String.join(",", formulas) + "]，" +
                    "[计算参数: " + params.toString() + "]" +
                    "[Error: " + e.getMessage() + "]";
            throw new RuntimeException(errorMsg);
        }
    }

    /**
     * 解析计算公式
     *
     * @param formulas 公式集合
     * @return 解析后的公式集合
     */
    public static List<String> parseFormulas(List<String> formulas) {
        List<String> result = new ArrayList<>();
        for (String formula : formulas) {
            if (formula.contains(EnumOperationSymbol.大于等于.getValue())) {
                formula = formula.replace(EnumOperationSymbol.大于等于.getValue(), EnumOperationSymbol.大于等于.getParseValue());
            } else if (formula.contains(EnumOperationSymbol.小于等于.getValue())) {
                formula = formula.replace(EnumOperationSymbol.小于等于.getValue(), EnumOperationSymbol.小于等于.getParseValue());
            } else if (formula.contains(EnumOperationSymbol.小于.getValue())) {
                formula = formula.replace(EnumOperationSymbol.小于.getValue(), EnumOperationSymbol.小于.getParseValue());
            } else if (formula.contains(EnumOperationSymbol.大于.getValue())) {
                formula = formula.replace(EnumOperationSymbol.大于.getValue(), EnumOperationSymbol.大于.getParseValue());
            }
            if (formula.contains(EnumOperationSymbol.并且.getValue())) {
                formula = formula.replace(EnumOperationSymbol.并且.getValue(), EnumOperationSymbol.并且.getParseValue());
            } else if (formula.contains(EnumOperationSymbol.或.getValue())) {
                formula = formula.replace(EnumOperationSymbol.或.getValue(), EnumOperationSymbol.或.getParseValue());
            }
            result.add(formula);
        }
        return result;
    }
}