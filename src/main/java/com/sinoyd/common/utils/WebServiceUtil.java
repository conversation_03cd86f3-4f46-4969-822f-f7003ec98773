package com.sinoyd.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.common.http.HTTPCaller;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

import java.util.Map;

/**
 * WebService工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/02/20
 **/
public class WebServiceUtil {


    /**
     * 调用Web服务
     *
     * @param url        Web服务的URL
     * @param soapAction SOAPAction头信息
     * @param body       SOAP消息的请求体
     * @param isHttps    是否使用HTTPS协议
     * @return 返回的JSON对象，包含Web服务调用的结果
     * @throws RuntimeException 如果调用失败或发生异常，将抛出此异常
     *
     *                          <p>此方法使用OkHttp库发送HTTP POST请求到指定的Web服务URL。
     *                          请求体包含SOAP消息的XML内容，并设置了相应的HTTP头信息，包括SOAPAction、Content-Type和cache-control。
     *                          如果调用成功（即HTTP响应的状态码表示成功），则将响应体（XML格式）转换为JSON对象并返回。
     *                          如果调用失败或发生异常，将抛出RuntimeException异常。</p>
     */
    public static JSONObject callWebService(String url, String soapAction, String body, boolean isHttps) {
        HTTPCaller httpCaller = HTTPCaller.getInstance(isHttps);
        OkHttpClient httpClient = httpCaller.getOkHttpClient();
        MediaType mediaType = MediaType.parse("text/xml; charset=utf-8");
        RequestBody requestBody = RequestBody.create(body, mediaType);
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("SOAPAction", soapAction)
                .addHeader("Content-Type", "text/xml; charset=utf-8")
                .addHeader("cache-control", "no-cache")
                .build();
        try (okhttp3.Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return XmlUtil.xml2Json(response.body() == null ? "" : response.body().string());
            } else {
                throw new RuntimeException(String.format("调用WebService: %s 失败，状态码：%s", url, response.code()));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 构建SOAP请求体
     *
     * @param soapRequestXml SOAP请求报文的基础XML字符串
     * @param paramMap       包含替换参数的键值对映射，键为需要替换的占位符，值为替换后的实际参数值
     * @return 构建后的SOAP请求体XML字符串
     * @throws RuntimeException 如果输入的SOAP请求报文 XML 为空，则抛出此异常
     */
    public static String buildSOAPRequestBody(String soapRequestXml, Map<String, Object> paramMap) {
        if (soapRequestXml == null || soapRequestXml.isEmpty()) {
            throw new RuntimeException("SOAP请求报文 XML 为空");
        }
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            soapRequestXml = soapRequestXml.replace("${" + entry.getKey() + "}", String.valueOf(entry.getValue()));
        }
        return soapRequestXml;
    }
}
