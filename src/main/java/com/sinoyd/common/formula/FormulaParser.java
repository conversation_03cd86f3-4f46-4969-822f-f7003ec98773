package com.sinoyd.common.formula;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 公式解析器
 * 负责解析公式中的变量
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/10
 */
public class FormulaParser {
    /**
     * 变量匹配模式
     */
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\[[^\\]]*\\]");

    /**
     * 解析公式中的变量
     *
     * @param formula 需要解析的公式
     * @return 变量列表
     */
    public List<FormulaVariable> parseVariables(String formula) {
        List<FormulaVariable> variables = new ArrayList<>();
        Matcher matcher = VARIABLE_PATTERN.matcher(formula);

        while (matcher.find()) {
            String varName = matcher.group(); // 包含[]的完整变量名
            int start = matcher.start();
            int end = matcher.end();

            String prevOperator = findPreviousOperator(formula, start);
            String nextOperator = findNextOperator(formula, end);

            variables.add(new FormulaVariable(varName, prevOperator, nextOperator));
        }

        return variables;
    }

    /**
     * 查找前置运算符
     *
     * @param formula 公式
     * @param start   变量起始位置
     * @return 前置运算符
     */
    private String findPreviousOperator(String formula, int start) {
        if (start <= 0) {
            return "";
        }

        char prevChar = formula.charAt(start - 1);
        if (isOperator(prevChar)) {
            return String.valueOf(prevChar);
        }

        // 如果前面是数字，则视为隐式乘法
        if (Character.isDigit(prevChar)) {
            return OperatorEnum.IMPLICIT_MULTIPLY.getSymbol();
        }

        return "";
    }

    /**
     * 查找后置运算符
     *
     * @param formula 公式
     * @param end     变量结束位置
     * @return 后置运算符
     */
    private String findNextOperator(String formula, int end) {
        if (end >= formula.length()) {
            return "";
        }

        char nextChar = formula.charAt(end);
        if (isOperator(nextChar)) {
            return String.valueOf(nextChar);
        }

        // 如果后面是数字，则视为隐式乘法
        if (Character.isDigit(nextChar)) {
            return OperatorEnum.IMPLICIT_MULTIPLY.getSymbol();
        }

        return "";
    }

    /**
     * 判断字符是否为运算符
     *
     * @param ch 需要判断的字符
     * @return 如果是运算符返回true，否则返回false
     */
    private boolean isOperator(char ch) {
        String symbol = String.valueOf(ch);
        OperatorEnum operator = OperatorEnum.getBySymbol(symbol);
        return operator != null && !operator.equals(OperatorEnum.IMPLICIT_MULTIPLY);
    }
} 