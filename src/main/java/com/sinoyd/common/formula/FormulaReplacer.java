package com.sinoyd.common.formula;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 公式替换器
 * 负责替换公式中的变量
 * 使用单例模式确保全局唯一实例
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/10
 */
public final class FormulaReplacer {
    /**
     * 单例实例
     */
    private static final FormulaReplacer INSTANCE = new FormulaReplacer();

    /**
     * 公式解析器
     */
    private final FormulaParser parser;

    /**
     * 变量处理器列表
     */
    private final List<FormulaVariableHandler> handlers;

    /**
     * 私有构造函数
     */
    private FormulaReplacer() {
        this.parser = new FormulaParser();
        this.handlers = new ArrayList<>();
        // 添加默认的变量处理器
        this.handlers.add(new DefaultVariableHandler());
    }

    /**
     * 获取单例实例
     *
     * @return FormulaReplacer实例
     */
    public static FormulaReplacer getInstance() {
        return INSTANCE;
    }

    /**
     * 添加变量处理器
     *
     * @param handler 变量处理器
     */
    public void addHandler(FormulaVariableHandler handler) {
        if (handler != null) {
            this.handlers.add(handler);
        }
    }

    /**
     * 替换公式中的变量
     *
     * @param formula   原始公式
     * @param variables 变量值映射
     * @return 替换后的公式
     */
    public String replace(String formula, Map<String, Object> variables) {
        if (formula == null || formula.isEmpty() || variables == null) {
            return formula;
        }

        String result = formula;
        List<FormulaVariable> vars = parser.parseVariables(result);

        for (FormulaVariable var : vars) {
            String varName = var.getNameWithoutBrackets();
            Object varValue = variables.get(varName);

            FormulaVariableHandler handler = findHandler(varValue != null ? varValue.toString() : null);
            if (handler != null) {
                String replacement = handler.handleVariable(var, result);
                result = result.replace(var.getName(), replacement);
            }
        }

        return result;
    }

    /**
     * 查找适合处理该变量值的处理器
     *
     * @param varValue 变量值
     * @return 变量处理器，如果没有找到返回null
     */
    private FormulaVariableHandler findHandler(String varValue) {
        if (varValue == null) {
            return null;
        }

        for (FormulaVariableHandler handler : handlers) {
            if (handler.canHandle(varValue)) {
                return handler;
            }
        }

        return null;
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        FormulaReplacer replacer = FormulaReplacer.getInstance();
        Map<String, Object> variables = new HashMap<>();

        // 测试用例1：基本运算
        variables.put("变量1", "/");
        variables.put("变量2", "/");
        String formula1 = "2*[变量1]+3*[变量2]";
        System.out.println("测试用例1：" + formula1);
        System.out.println("替换结果：" + replacer.replace(formula1, variables));

        // 测试用例2：连续变量
        String formula2 = "[变量1][变量2]";
        System.out.println("\n测试用例2：" + formula2);
        System.out.println("替换结果：" + replacer.replace(formula2, variables));

        // 测试用例3：变量前后有数字
        String formula3 = "2[变量1]3";
        System.out.println("\n测试用例3：" + formula3);
        System.out.println("替换结果：" + replacer.replace(formula3, variables));

        // 测试用例4：复杂公式
        variables.put("浓度", "/");
        variables.put("流量", "/");
        String formula4 = "2*[浓度]*[流量]/(2+[浓度])";
        System.out.println("\n测试用例4：" + formula4);
        System.out.println("替换结果：" + replacer.replace(formula4, variables));

        // 测试用例5：嵌套括号
        String formula5 = "(2+[变量1])*(3+[变量2])";
        System.out.println("\n测试用例5：" + formula5);
        System.out.println("替换结果：" + replacer.replace(formula5, variables));

        // 测试用例6：pH计算公式
        variables.clear();
        variables.put("pH1", "/");
        variables.put("pH2", "/");
        String formula6 = "(Pow(10,(10*[pH1]+10*[pH2])/2))/1000000";
        System.out.println("\n测试用例6：pH计算公式");
        System.out.println("原始公式：" + formula6);
        System.out.println("替换结果：" + replacer.replace(formula6, variables));

        // 测试用例7：硝酸银滴定计算公式
        variables.clear();
        variables.put("硝酸银标液浓度", "/");
        variables.put("硝酸银标液消耗量", "/");
        variables.put("滴定空白1", "/");
        variables.put("滴定空白2", "/");
        variables.put("取样体积", "/");
        variables.put("稀释倍数", "/");
        String formula7 = "[硝酸银标液浓度]*([硝酸银标液消耗量]-([滴定空白1]+[滴定空白2])/2)*35.45*1000/[取样体积]*[稀释倍数]";
        System.out.println("\n测试用例7：硝酸银滴定计算公式");
        System.out.println("原始公式：" + formula7);
        System.out.println("替换结果：" + replacer.replace(formula7, variables));

        // 测试用例8：土壤提取计算公式
        variables.clear();
        variables.put("吸光度", "/");
        variables.put("空白", "/");
        variables.put("b", "/");
        variables.put("k", "/");
        variables.put("空白试样", "/");
        variables.put("分取提取液体积", "/");
        variables.put("提取液体积", "/");
        variables.put("土壤取样量", "/");
        variables.put("干物质含量", "/");
        variables.put("水的密度", "/");
        variables.put("稀释倍数", "/");
        String formula8 = "((([吸光度]-[空白]-[b])/[k])-(([空白试样]-[空白]-[b])/[k]))/[分取提取液体积]*(([提取液体积]+[土壤取样量]*(1-[干物质含量])/[水的密度])/([土壤取样量]*[干物质含量]))*[稀释倍数]";
        System.out.println("\n测试用例8：土壤提取计算公式");
        System.out.println("原始公式：" + formula8);
        System.out.println("替换结果：" + replacer.replace(formula8, variables));
    }
} 