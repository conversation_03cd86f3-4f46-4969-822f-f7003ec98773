package com.sinoyd.common.formula;

import lombok.Getter;

/**
 * 公式变量类
 * 表示公式中的变量及其运算符
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/10
 */
@Getter
public class FormulaVariable {
    /**
     * 变量名（包含[]）
     */
    private final String name;

    /**
     * 前置运算符
     */
    private final String prevOperator;

    /**
     * 后置运算符
     */
    private final String nextOperator;

    /**
     * 构造函数
     *
     * @param name         变量名
     * @param prevOperator 前置运算符
     * @param nextOperator 后置运算符
     */
    public FormulaVariable(String name, String prevOperator, String nextOperator) {
        this.name = name;
        this.prevOperator = prevOperator;
        this.nextOperator = nextOperator;
    }

    /**
     * 获取不带[]的变量名
     *
     * @return 变量名
     */
    public String getNameWithoutBrackets() {
        return name.substring(1, name.length() - 1);
    }
} 