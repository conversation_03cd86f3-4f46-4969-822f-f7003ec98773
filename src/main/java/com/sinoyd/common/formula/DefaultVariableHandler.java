package com.sinoyd.common.formula;

/**
 * 默认的变量处理器
 * 处理值为"/"的变量
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/10
 */
public class DefaultVariableHandler implements FormulaVariableHandler {

    @Override
    public String handleVariable(FormulaVariable variable, String formula) {
        // 获取前后运算符对应的枚举
        OperatorEnum prevOp = OperatorEnum.getBySymbol(variable.getPrevOperator());
        OperatorEnum nextOp = OperatorEnum.getBySymbol(variable.getNextOperator());

        // 如果前后运算符有一个是加减，则替换为0
        if ((prevOp != null && prevOp.isAddOrSubtract()) ||
                (nextOp != null && nextOp.isAddOrSubtract())) {
            return OperatorEnum.ADD.getReplaceValue();
        }

        // 如果变量前后是数字（没有明确的运算符），则视为乘法运算
        int varStart = formula.indexOf(variable.getName());
        int varEnd = varStart + variable.getName().length();

        if ((varStart > 0 && Character.isDigit(formula.charAt(varStart - 1))) ||
                (varEnd < formula.length() && Character.isDigit(formula.charAt(varEnd)))) {
            return OperatorEnum.MULTIPLY.getReplaceValue();
        }

        // 否则替换为1（参与乘除运算）
        return OperatorEnum.MULTIPLY.getReplaceValue();
    }

    @Override
    public boolean canHandle(String varValue) {
        return "/".equals(varValue);
    }
} 