package com.sinoyd.common.formula;

import lombok.Getter;

/**
 * 运算符枚举
 * 定义运算符及其对应的替换规则
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/10
 */
@Getter
public enum OperatorEnum {
    /**
     * 加法运算
     */
    ADD("+", "0", true),

    /**
     * 减法运算
     */
    SUBTRACT("-", "0", true),

    /**
     * 乘法运算
     */
    MULTIPLY("*", "1", false),

    /**
     * 除法运算
     */
    DIVIDE("/", "1", false),

    /**
     * 隐式乘法（数字相邻）
     */
    IMPLICIT_MULTIPLY("", "1", false);

    /**
     * 运算符符号
     */
    private final String symbol;

    /**
     * 替换值
     */
    private final String replaceValue;

    /**
     * 是否为加减运算
     */
    private final boolean isAddSubtract;

    /**
     * 构造函数
     *
     * @param symbol        运算符符号
     * @param replaceValue  替换值
     * @param isAddSubtract 是否为加减运算
     */
    OperatorEnum(String symbol, String replaceValue, boolean isAddSubtract) {
        this.symbol = symbol;
        this.replaceValue = replaceValue;
        this.isAddSubtract = isAddSubtract;
    }

    /**
     * 根据符号获取运算符枚举
     *
     * @param symbol 运算符符号
     * @return 对应的运算符枚举，如果未找到则返回null
     */
    public static OperatorEnum getBySymbol(String symbol) {
        if (symbol == null || symbol.isEmpty()) {
            return null;
        }
        for (OperatorEnum operator : values()) {
            if (operator.getSymbol().equals(symbol)) {
                return operator;
            }
        }
        return null;
    }

    /**
     * 判断是否为加减运算
     *
     * @return 如果是加减运算返回true，否则返回false
     */
    public boolean isAddOrSubtract() {
        return this.isAddSubtract;
    }

    /**
     * 判断是否为乘除运算
     *
     * @return 如果是乘除运算返回true，否则返回false
     */
    public boolean isMultiplyOrDivide() {
        return !this.isAddSubtract;
    }
} 