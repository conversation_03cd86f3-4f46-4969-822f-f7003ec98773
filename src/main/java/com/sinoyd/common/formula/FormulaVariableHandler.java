package com.sinoyd.common.formula;

/**
 * 公式变量处理接口
 * 定义了处理公式变量的基本行为
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/10
 */
public interface FormulaVariableHandler {

    /**
     * 处理变量
     *
     * @param variable 变量对象
     * @param formula  完整公式
     * @return 替换值
     */
    String handleVariable(FormulaVariable variable, String formula);

    /**
     * 判断是否可以处理该变量
     *
     * @param varValue 变量值
     * @return 如果可以处理返回true，否则返回false
     */
    boolean canHandle(String varValue);
} 