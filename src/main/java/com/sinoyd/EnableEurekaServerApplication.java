package com.sinoyd;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.JdkDateSupport;
import com.sinoyd.frame.configuration.JdkTimestampSupport;
import com.sinoyd.grpc.start.GrpcStartService;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.WebApplicationInitializer;

import javax.servlet.MultipartConfigElement;

/**
 * spring cloud系统启动入口
 */
@SpringBootApplication
@EnableJpaAuditing(auditorAwareRef = "principalContextUser")
@EnableAsync
public class EnableEurekaServerApplication extends SpringBootServletInitializer implements WebApplicationInitializer {

    public static void main(String[] args) throws Exception {
        ApplicationContext applicationContext = new SpringApplicationBuilder(EnableEurekaServerApplication.class).web(true).run(args);
        JdkTimestampSupport.enable(DateUtil.FULL);//启用json时间序列化
        JdkDateSupport.enable(DateUtil.FULL);//启用json时间序列化格式
        GrpcStartService grpcServer = applicationContext.getBean(GrpcStartService.class);
        grpcServer.startGrpc();
    }

    /**
     * 文件上传配置
     *
     * @return
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        //文件最大
        factory.setMaxFileSize("1024000KB"); //KB,MB
        /// 设置总上传数据总大小
        factory.setMaxRequestSize("1024000KB");
        return factory.createMultipartConfig();
    }
}
