package com.sinoyd.parse.dto;

import lombok.AllArgsConstructor;
import lombok.Data;


/**
 * 解析流应用及仪器配置信息dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Data
@AllArgsConstructor
public class DtoStreamAppInstrumentConfig {
    private static final long serialVersionUID = 1L;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 应用名称
     */
    private String appName;

    /**

     * 方案id
     */
    private String planId;

    /**
     * 方案名称
     */
    private String planName;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 制造厂商
     */
    private String manufacturer;

    /**
     * 解析类型（0：文本图片 1：文本 2：爱华声级计 5688  3：爱华声级计 6228）
     */
    private Integer analysisType;

    /**
     * 硬件流控
     */
    private Boolean fluidControl;

    /**
     * 应答周期(ms)
     */
    private Integer responsePeriod;

    /**
     * 应答
     */
    private Boolean response;

    /**
     * 波特率
     */
    private Integer baudRate;

    /**
     * RS232串口(是否交叉 0：直连 1:交叉  )
     */
    private Boolean cross;

    /**
     * USB串口波特率
     */
    private Integer usbBaudRate;

    /**
     *Pin码
     */
    private String pin;


    public DtoStreamAppInstrumentConfig(String appName, String planId) {
        this.appName = appName;
        this.planId = planId;
    }

    public DtoStreamAppInstrumentConfig(String appName, String planId, String instrumentName, String instrumentCode,
                                        String manufacturer, Integer analysisType, Boolean fluidControl, Integer responsePeriod,
                                        Boolean response, Integer baudRate, Boolean cross, Integer usbBaudRate, String pin) {
        this.appName = appName;
        this.planId = planId;
        this.instrumentName = instrumentName;
        this.instrumentCode = instrumentCode;
        this.manufacturer = manufacturer;
        this.analysisType = analysisType;
        this.fluidControl = fluidControl;
        this.responsePeriod = responsePeriod;
        this.response = response;
        this.baudRate = baudRate;
        this.cross = cross;
        this.usbBaudRate = usbBaudRate;
        this.pin = pin;
    }

}