package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.StreamParamConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 解析流方案参数配置dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_StreamParamConfig")
@Data
@DynamicInsert
public class DtoStreamParamConfig extends StreamParamConfig {
    private static final long serialVersionUID = 1L;

    public DtoStreamParamConfig() {
    }

    public DtoStreamParamConfig(String id, String paramName, String paramAlias, Integer paramType) {
        setId(id);
        setParamName(paramName);
        setParamAlias(paramAlias);
        setParamType(paramType);
    }
}