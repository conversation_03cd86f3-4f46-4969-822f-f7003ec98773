package com.sinoyd.parse.dto;

import lombok.AllArgsConstructor;
import lombok.Data;


/**
 * 移动端同步解析流应用dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/29
 * @since V100R001
 */
@Data
@AllArgsConstructor
public class DtoStreamAppMobileSync {
    private static final long serialVersionUID = 1L;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 制造厂商
     */
    private String manufacturer;

    /**
     * 解析类型（0：文本图片 1：文本 2：爱华声级计 5688  3：爱华声级计 6228）
     */
    private Integer analysisType;

    /**
     * 硬件流控
     */
    private Boolean fluidControl;

    /**
     * 应答
     */
    private Boolean response;

    /**
     * 应答周期(ms)
     */
    private Integer responsePeriod;

    /**
     * RS232串口(是否交叉 0：直连 1:交叉 )
     */
    private Boolean cross;

    /**
     * 波特率
     */
    private Integer baudRate;


    /**
     * USB串口波特率
     */
    private Integer usbBaudRate;

    /**
     *Pin码
     */
    private String pin;


}