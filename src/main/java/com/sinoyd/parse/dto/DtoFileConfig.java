package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.FileConfig;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoFileConfig实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_FileConfig")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoFileConfig extends FileConfig {
    private static final long serialVersionUID = 1L;

    @Transient
    private String fileTypeName;
    @Transient
    private String handleTypeName;
}