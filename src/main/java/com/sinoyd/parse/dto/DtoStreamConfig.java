package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.StreamConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 解析流方案配置dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_StreamConfig")
@Data
@DynamicInsert
public class DtoStreamConfig extends StreamConfig {
    private static final long serialVersionUID = 1L;

    public DtoStreamConfig(){

    }

    public DtoStreamConfig(String id, String planName, String textSplit, boolean batchStream, String enCode){
        setId(id);
        setPlanName(planName);
        setTextSplit(textSplit);
        setBatchStream(batchStream);
        setEnCode(enCode);
    }


}