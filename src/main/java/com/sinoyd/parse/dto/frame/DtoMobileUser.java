package com.sinoyd.parse.dto.frame;

import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 用户信息dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/08/9
 * @since V100R001
 */
@Entity
@Table(name = "t_sys_user")
@Data
@DynamicInsert
public class DtoMobileUser {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column
    private String id = UUIDHelper.NewID();

    @ApiModelProperty("账号")
    private String loginID;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("姓名")
    private String displayName;

    @ApiModelProperty("工号")
    private String empNum;

    @ApiModelProperty("性别（0：女、1：男）")
    private String sexCode;

    @ApiModelProperty("生日")
    private Date birthday;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("联系电话")
    private String telephone;

    @ApiModelProperty("用户类型（数据字典：sys_user_type）")
    private String userTypeCode;

    @ApiModelProperty("首页地址")
    private String homeUrl;

    @ApiModelProperty("皮肤（数据字典：sys_theme_type）")
    private String themeCode;

    @ApiModelProperty("客户的用户中心id标识")
    private String centerId;

    @ApiModelProperty("企业头像")
    private String picture;


    private boolean enabled;

    private Integer sortNum;

    private String note;

    private Date createDate;

    private String createUserGuid;

    private String createUserName;

    @Column(name = "updateDate")
    private Date modifyDate;

    private String updateUserGuid;

    private String updateUserName;

    @ApiModelProperty("所属机构Guid")
    private String orgGuid;

    @Column(name = "deleted")
    private Boolean isDeleted = false;

    public DtoMobileUser(String id, String loginID, String password, String displayName, String orgGuid) {
        this.id = id;
        this.loginID = loginID;
        this.password = password;
        this.displayName = displayName;
        this.orgGuid = orgGuid;
    }
}
