package com.sinoyd.parse.dto.customer;


import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 实验室仪器解析方案流程配置导入导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtoExpImpFileConfigFlow implements IExcelModel, IExcelDataModel {

    /**
     * 行号
     */
    private int rowNum;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10", width = 20)
    private String id;

    /**
     * 编号
     */
    @Excel(name = "方案名称", orderNum = "20", width = 24)
    private String planName;

    /**
     * 文件类型(1:txt;2:excel;3:word;4:pdf 5:mdb)
     */
    @Excel(name = "文件类型(1:txt;2:excel;3:word;4:pdf 5:mdb)", orderNum = "30", width = 17)
    private String fileType;

    /**
     * 编码格式（空时为默认编码格式）
     */
    @Excel(name = "编码格式（空时为默认编码格式）", orderNum = "40", width = 15)
    private String fileCode;

    /**
     * 处理方式(解析,上传)
     */
    @Excel(name = "规格", orderNum = "50", width = 13)
    private String handleType;

    /**
     * 方案附件路径
     */
    @Excel(name = "方案附件路径", orderNum = "60", width = 20)
    private String planFile;

    /**
     * 流程id
     */
    @Excel(name = "流程id", orderNum = "65", width = 25)
    private String flowId;

    /**
     * 流程名称
     */
    @Excel(name = "流程名称", orderNum = "70", width = 25)
    private String flowName;

    /**
     * 解析类型(1:样品编号，2:分析项目名称，3:参数 4:其他)
     */
    @Excel(name = "解析类型(1:样品编号，2:分析项目名称，3:参数 4:其他)", orderNum = "80", width = 15)
    private String parseType;

    /**
     * 步骤编号
     */
    @Excel(name = "步骤编号", orderNum = "90", width = 18)
    private String stepNumStr;

    private Integer stepNum;

    /**
     * 特殊类(处理特殊解析的类)
     */
    @Excel(name = "特殊类(处理特殊解析的类)", orderNum = "100", width = 15)
    private String specialClass;

    /**
     * 是否使用上一步数据（是，否）
     */
    @Excel(name = "是否使用上一步数据（是，否）", orderNum = "110", width = 14)
    private String useLastDataStr;

    private Boolean useLastData;

    /**
     * 使用方式(1:拼接 2:替换)
     */
    @Excel(name = "使用方式(1:拼接 2:替换)", orderNum = "120", width = 15)
    private String useType;
}
