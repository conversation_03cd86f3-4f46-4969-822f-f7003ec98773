package com.sinoyd.parse.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实验室仪器解析-解析方案结果映射导入/导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/12/09
 * @since V100R001
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtoImportFileResultAlais implements IExcelModel, IExcelDataModel {

    /**
     * 行号
     */
    private int rowNum;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 结果类型（1:分析项目 2:参数）
     */
    @Excel(name = "结果类型(必填)", orderNum = "100", width = 24)
    private String resultType;

    /**
     * 结果原始名称
     */
    @Excel(name = "结果原始名称(必填)", orderNum = "200", width = 30)
    private String paramName;

    /**
     * 结果映射名称
     */
    @Excel(name = "结果映射名称(必填)", orderNum = "300", width = 30)
    private String paramAlias;
}
