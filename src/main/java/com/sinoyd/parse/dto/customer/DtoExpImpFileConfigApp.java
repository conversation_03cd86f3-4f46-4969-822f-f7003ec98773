package com.sinoyd.parse.dto.customer;


import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实验室仪器解析方案解析应用导入导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtoExpImpFileConfigApp implements IExcelModel, IExcelDataModel {

    /**
     * 行号
     */
    private int rowNum;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10", width = 20)
    private String id;

    /**
     * 方案名称
     */
    @Excel(name = "方案名称", orderNum = "20", width = 24)
    private String planName;

    /**
     * 文件类型(1:txt;2:excel;3:word;4:pdf 5:mdb)
     */
    @Excel(name = "文件类型(1:txt;2:excel;3:word;4:pdf 5:mdb)", orderNum = "30", width = 17)
    private String fileType;

    /**
     * 编码格式（空时为默认编码格式）
     */
    @Excel(name = "编码格式（空时为默认编码格式）", orderNum = "40", width = 15)
    private String fileCode;

    /**
     * 处理方式(解析,上传)
     */
    @Excel(name = "处理方式(解析,上传)", orderNum = "50", width = 13)
    private String handleType;

    /**
     * 方案附件路径
     */
    @Excel(name = "方案附件路径", orderNum = "60", width = 20)
    private String planFile;

    /**
     * 应用id
     */
    @Excel(name = "应用id", orderNum = "65", width = 20)
    private String appId;

    /**
     * 应用名称
     */
    @Excel(name = "应用名称", orderNum = "70", width = 20)
    private String appName;

    /**
     * 仪器Id
     */
    @Excel(name = "仪器Id", orderNum = "80", width = 20)
    private String instrumentId;

    /**
     * 仪器名称
     */
    @Excel(name = "仪器名称", orderNum = "90", width = 20)
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Excel(name = "仪器编号", orderNum = "100", width = 20)
    private String instrumentCode;

    /**
     * 解析类型（1：文件解析 2：数据流解析 3:文件解析调试）
     */
    @Excel(name = "解析类型（1：文件解析 2：数据流解析 3:文件解析调试）", orderNum = "110", width = 15)
    private String parseType;

    /**
     * 监听文件夹（文件夹名称，文件解析时必填）
     */
    @Excel(name = "监听文件夹（文件夹名称，文件解析时必填）", orderNum = "90", width = 20)
    private String folderName;

    /**
     * 解析成功存放目录
     */
    @Excel(name = "解析成功存放目录", orderNum = "90", width = 20)
    private String sucFolderName;

    /**
     * 解析失败存放目录
     */
    @Excel(name = "解析失败存放目录", orderNum = "100", width = 20)
    private String failFolderName;
}
