package com.sinoyd.parse.dto.customer;

import com.sinoyd.parse.dto.DtoAppFlowData;
import com.sinoyd.parse.dto.DtoDatas;
import com.sinoyd.parse.dto.DtoErrorLog;
import lombok.Data;

import java.util.List;

/**
 * DtoParseLog实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@Data
public class DtoFileParseLog {

    private List<DtoAppFlowData> appFlowDataList;

    private List<DtoErrorLog> errorLogList;

    private List<DtoDatas> dtoDatasList;
}
