package com.sinoyd.parse.dto.customer;


import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实验室仪器解析方案结果映射导入导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtoExpImpFileConfigAlais implements IExcelModel, IExcelDataModel {

    /**
     * 行号
     */
    private int rowNum;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10", width = 20)
    private String id;

    /**
     * 编号
     */
    @Excel(name = "方案名称", orderNum = "20", width = 24)
    private String planName;

    /**
     * 文件类型(1:txt;2:excel;3:word;4:pdf 5:mdb)
     */
    @Excel(name = "文件类型(1:txt;2:excel;3:word;4:pdf 5:mdb)", orderNum = "30", width = 17)
    private String fileType;

    /**
     * 编码格式（空时为默认编码格式）
     */
    @Excel(name = "编码格式（空时为默认编码格式）", orderNum = "40", width = 15)
    private String fileCode;

    /**
     * 处理方式(解析,上传)
     */
    @Excel(name = "规格", orderNum = "50", width = 13)
    private String handleType;

    /**
     * 方案附件路径
     */
    @Excel(name = "方案附件路径", orderNum = "60", width = 20)
    private String planFile;

    /**
     * 结果映射id
     */
    @Excel(name = "结果映射id", orderNum = "70", width = 23)
    private String resultAliasId;

    /**
     * 结果原始名
     */
    @Excel(name = "结果原始名", orderNum = "80", width = 23)
    private String paramName;

    /**
     * 结果映射名
     */
    @Excel(name = "结果映射名", orderNum = "90", width = 22)
    private String paramAlias;

    /**
     * 结果类型(1:分析项目名称，2:参数)
     */
    @Excel(name = "结果类型(1:分析项目名称，2:参数)", orderNum = "100", width = 22)
    private String resultType;
}
