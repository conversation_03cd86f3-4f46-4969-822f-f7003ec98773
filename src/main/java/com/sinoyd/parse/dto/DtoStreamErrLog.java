package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.StreamErrLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


/**
 * 解析流错误日志dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_StreamErrorLog")
@Data
@DynamicInsert
public class DtoStreamErrLog extends StreamErrLog {
    private static final long serialVersionUID = 1L;

    public DtoStreamErrLog(String id, String errorType, String logContent, Date errorTime){
        setId(id);
        setErrorType(errorType);
        setLogContent(logContent);
        setErrorTime(errorTime);
    }

//    a.errorType, a.errorTime, a.logContent
    public DtoStreamErrLog(String errorType, Date errorTime, String logContent){
        setErrorType(errorType);
        setErrorTime(errorTime);
        setLogContent(logContent);
    }

}