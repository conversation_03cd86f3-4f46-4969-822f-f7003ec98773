package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.StreamData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


/**
 * 解析流数据dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_StreamDatas")
@Data
@DynamicInsert
public class DtoStreamData extends StreamData {
    private static final long serialVersionUID = 1L;

    @Transient
    private Date getTime;

    @Transient
    private Date parseDataTime;

    @Transient
    private String appName;

    @Transient
    private Integer orderNum;

    public DtoStreamData(String serialNumber, Integer paramType, String paramName, String paramAlias, String value, String unit, Date getTime,
                         Date parseDataTime, String instrumentName, String instrumentCode, String appName) {
        setSerialNumber(serialNumber);
        setParamType(paramType);
        setParamName(paramName);
        setParamAlias(paramAlias);
        setValue(value);
        setUnit(unit);
        setGetTime(getTime);
        setParseDataTime(parseDataTime);
        setInstrumentName(instrumentName);
        setInstrumentCode(instrumentCode);
        setAppName(appName);

    }

    public DtoStreamData(String paramName, String paramAlias, String value, String unit,Integer paramType, Integer orderNum) {
        setParamName(paramName);
        setParamAlias(paramAlias);
        setValue(value);
        setParamType(paramType);
        setUnit(unit);
        setOrderNum(orderNum);
    }

    public DtoStreamData(String serialNumber, String instrumentName, String instrumentCode, String paramName, String paramAlias, String value) {
        setSerialNumber(serialNumber);
        setInstrumentName(instrumentName);
        setInstrumentCode(instrumentCode);
        setParamName(paramName);
        setParamAlias(paramAlias);
        setValue(value);
    }

    public DtoStreamData(String serialNumber, Date getTime, String instrumentName, String instrumentCode,
                         String paramName, String paramAlias, String value, String unit) {
        setSerialNumber(serialNumber);
        setGetTime(getTime);
        setInstrumentName(instrumentName);
        setInstrumentCode(instrumentCode);
        setParamName(paramName);
        setParamAlias(paramAlias);
        setValue(value);
        setUnit(unit);
    }

}