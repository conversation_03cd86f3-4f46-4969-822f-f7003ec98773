package com.sinoyd.parse.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;


/**
 * 移动端查询解析数据结果dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/30
 * @since V100R001
 */
@Data
@AllArgsConstructor
public class DtoMobileStreamData {
    private static final long serialVersionUID = 1L;

    /**
     * 流水号
     */
    private String serialNumber;

    /**
     * 采集时间
     */
    private Date getTime;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器编号
     */
    private String instrumentCode;

    /**
     * 参数信息列表
     */
    private List<DtoMobileParam> paramList;

    public DtoMobileStreamData(String serialNumber, String instrumentName, String instrumentCode, List<DtoMobileParam> paramList) {
        this.serialNumber = serialNumber;
        this.instrumentName = instrumentName;
        this.instrumentCode = instrumentCode;
        this.paramList = paramList;
    }

    public DtoMobileStreamData(String serialNumber, Date getTime) {
        this.serialNumber = serialNumber;
        this.getTime = getTime;
    }

}