package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.StreamApp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * 解析流应用dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_StreamApp")
@Data
@DynamicInsert
public class DtoStreamApp extends StreamApp {
    private static final long serialVersionUID = 1L;

    @Transient
    private String planName;

    public DtoStreamApp() {
    }

    public DtoStreamApp(String appName, String instrumentId, String instrumentName, String instrumentCode, String manufacturer, String planId, String instrumentConfigId) {
        super(appName, instrumentId, instrumentName, instrumentCode, manufacturer, planId, instrumentConfigId);
    }

//    a.id, a.appName, a.instrumentName, a.instrumentCode, b.planName

    public DtoStreamApp(String id, String appName, String instrumentName, String instrumentCode, String planName) {
        setId(id);
        setAppName(appName);
        setInstrumentName(instrumentName);
        setInstrumentCode(instrumentCode);
        setPlanName(planName);
    }
}