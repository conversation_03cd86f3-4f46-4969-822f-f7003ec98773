package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.FileApp;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoFileApp实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_FileApp")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoFileApp extends FileApp {
    private static final long serialVersionUID = 1L;
    /**
     * 方案名
     */
    @Transient
    private String planName;
    @Transient
    private String parseTypeName;
}