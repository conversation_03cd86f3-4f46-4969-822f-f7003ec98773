package com.sinoyd.parse.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import java.util.Date;


/**
 * 移动端查询解析数据参数信息dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/30
 * @since V100R001
 */
@Data
@AllArgsConstructor
public class DtoMobileParam {
    private static final long serialVersionUID = 1L;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 参数别名
     */
    private String paramAlias;

    /**
     * 参数值
     */
    private String value;

    /**
     * 参数单位
     */
    private String paramUnit;


}