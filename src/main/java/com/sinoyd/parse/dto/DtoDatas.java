package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.Datas;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoDatas实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_Datas")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoDatas extends Datas {
    private static final long serialVersionUID = 1L;
    /**
     * 应用名
     */
    @Transient
    private String appName;
    /**
     * 文件名
     */
    @Transient
    private String fileName;

    /**
     * 单位名称
     */
    @Transient
    private String orgName;
}