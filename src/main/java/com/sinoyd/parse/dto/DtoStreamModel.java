package com.sinoyd.parse.dto;

import lombok.AllArgsConstructor;
import lombok.Data;


/**
 * 移动端同步采集数据信息dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/30
 * @since V100R001
 */
@Data
public class DtoStreamModel {
    private static final long serialVersionUID = 1L;

    /**
     * 应用id
     */
    private String AppId;

    /**
     * 采集时间
     */
    private String GetTime;

    /**
     * 上传时间
     */
    private String PostTime;

    /**
     * 采集数据内容
     */
    private String Content;

    /**
     * 组织机构id
     */
    private String OrgId;

    /**
     * 创建人
     */
    private String Creator;

    public DtoStreamModel() {
    }

    public DtoStreamModel(String appId, String getTime, String postTime, String content, String orgId, String creator) {
        this.AppId = appId;
        this.GetTime = getTime;
        this.PostTime = postTime;
        this.Content = content;
        this.OrgId = orgId;
        this.Creator = creator;
    }


}