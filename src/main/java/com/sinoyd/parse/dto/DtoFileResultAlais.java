package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.FileResultAlais;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoFileResultAlais实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_FileResultAlais")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoFileResultAlais extends FileResultAlais {
    private static final long serialVersionUID = 1L;

    @Transient
    private String resultTypeName;
}