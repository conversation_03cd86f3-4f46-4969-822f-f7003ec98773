package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.InstrumentConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 解析流仪器配置dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_InstrumentConfig")
@Data
@DynamicInsert
public class DtoInstrumentConfig extends InstrumentConfig {
    private static final long serialVersionUID = 1L;

//    @Transient
//    private String appId;


    public DtoInstrumentConfig() {
    }

    public DtoInstrumentConfig(String name, String bleServerName, Boolean logOut, Integer analysisType, Boolean fluidControl,
                               Boolean response, Integer responsePeriod, Boolean rsCross, Integer baudRate, Integer usbBaudRate, String pin) {
        super(name, bleServerName, logOut, analysisType, fluidControl, response, responsePeriod, rsCross, baudRate, usbBaudRate, pin);
    }
}