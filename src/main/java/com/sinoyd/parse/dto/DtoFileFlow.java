package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.FileFlow;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoFileFlow实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_FileFlow")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoFileFlow extends FileFlow {
    private static final long serialVersionUID = 1L;

    @Transient
    private String parseTypeName;
    @Transient
    private String useTypeName;
}