package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.OrgStream;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 源数据流dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_OrgStream")
@Data
@DynamicInsert
public class DtoOrgStream extends OrgStream {
    private static final long serialVersionUID = 1L;

    public DtoOrgStream(){

    }

    public DtoOrgStream(String streamContent) {
        setStreamContent(streamContent);
    }

}