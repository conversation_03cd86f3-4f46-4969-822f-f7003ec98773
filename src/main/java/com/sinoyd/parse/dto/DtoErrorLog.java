package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.ErrorLog;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoErrorLog实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_ErrorLog")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoErrorLog extends ErrorLog {
    private static final long serialVersionUID = 1L;
    /**
     * 流程名
     */
    @Transient
    private String flowName;
}