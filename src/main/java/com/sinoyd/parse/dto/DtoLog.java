package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.Log;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.List;


/**
 * DtoLog实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_Log")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoLog extends Log {
    private static final long serialVersionUID = 1L;

    @Transient
    private List<DtoAppFlowData> flowDataList;
    /**
     * 解析类型名称
     */
    @Transient
    private String parseTypeName;
    /**
     * 解析状态名称
     */
    @Transient
    private String parseStatusName;
    /**
     * 处理类型名
     */
    @Transient
    private String handleTypeName;
    /**
     * 仪器名
     */
    @Transient
    private String instrumentName;
    /**
     * 仪器编码
     */
    @Transient
    private String instrumentCode;
}