package com.sinoyd.parse.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.dto.DtoMobileStreamData;
import com.sinoyd.parse.dto.DtoStreamConfig;
import com.sinoyd.parse.dto.DtoStreamData;

import java.util.List;


/**
 * 解析流数据操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface StreamDataService extends IBaseJpaService<DtoStreamData, String> {

//    /**
//     * 移动端按照采集时间，仪器关键字查询解析数据信息(分页)
//     * @param pageBean 分页信息对象
//     * @param streamDataMobileCriteria 条件参数对象
//     * @return List<DtoMobileStreamData>
//     */
//    List<DtoMobileStreamData> findMobileStreamDataByPage(PageBean<DtoMobileStreamData> pageBean, BaseCriteria streamDataMobileCriteria);

    /**
     * 移动端按照采集时间，仪器关键字查询解析数据信息
     * @param streamDataMobileCriteria 条件参数对象
     * @return List<DtoMobileStreamData>
     */
    List<DtoMobileStreamData> findMobileStreamData(BaseCriteria streamDataMobileCriteria);

}