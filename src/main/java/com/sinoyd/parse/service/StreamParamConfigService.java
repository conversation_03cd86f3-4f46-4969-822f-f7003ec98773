package com.sinoyd.parse.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.dto.DtoStreamParamConfig;

import java.util.List;


/**
 * 解析流方案参数配置操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface StreamParamConfigService extends IBaseJpaService<DtoStreamParamConfig, String> {



    /**
     * 按照方案参数配置主键id查询方案参数信息
     * @param id   解析流方案参数配置主键id
     * @return DtoStreamParamConfig
     */
    DtoStreamParamConfig findStreamParamConfig(String id);

    /**
     * 新增解析流方案参数配置
     *
     * @param entity 解析流方案参数信息实体对象
     * @return DtoStreamParamConfig
     */
    DtoStreamParamConfig saveStreamParamConfig(DtoStreamParamConfig entity);

    /**
     * 更新解析流方案参数信息
     *
     * @param entity 解析流方案参数信息实体对象
     * @return DtoStreamParamConfig
     */
    DtoStreamParamConfig updateStreamParamConfig(DtoStreamParamConfig entity);


    /**
     * 按照解析流方案id列表 删除解析流方案信息
     *
     * @param ids 解析流方案信息对象列表
     * @return 删除的记录数
     */
    Integer deleteByIds(List<String> ids);

}