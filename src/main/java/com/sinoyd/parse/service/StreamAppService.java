package com.sinoyd.parse.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.dto.DtoStreamApp;
import com.sinoyd.parse.dto.DtoStreamAppInstrumentConfig;
import com.sinoyd.parse.dto.DtoStreamAppMobileSync;

import java.util.List;


/**
 * 解析流应用操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface StreamAppService extends IBaseJpaService<DtoStreamApp, String> {

    /**
     * 按照主键id查询解析流应用信息
     *
     * @param id 解析流应用主键id
     * @return DtoStreamAppInstrumentConfig
     */
    DtoStreamAppInstrumentConfig findStreamApp(String id);

    /**
     * 查询移动端解析流应用信息
     *
     * @return List<DtoStreamAppMobileSync>
     */
    List<DtoStreamAppMobileSync> findMobileStreamApp();

    /**
     * 新增解析流应用配置
     *
     * @param dtoStreamAppInstrumentConfig 解析流应用及仪器配置信息对象
     * @return DtoStreamAppInstrumentConfig
     */
    DtoStreamAppInstrumentConfig saveStreamApp(DtoStreamAppInstrumentConfig dtoStreamAppInstrumentConfig);

    /**
     * 复制解析流应用配置信息
     *
     * @param ids 解析流应用id列表
     * @return 删除的记录数
     */
    List<DtoStreamApp> copyStreamApp(List<String> ids);

    /**
     * 更新解析流应用信息
     *
     * @param entity 解析流应用信息实体对象
     * @return DtoStreamParamConfig
     */
    DtoStreamAppInstrumentConfig updateStreamApp(DtoStreamAppInstrumentConfig entity);


    /**
     * 按照解析流方案id列表 删除解析流方案信息
     *
     * @param ids 解析流方案信息对象列表
     * @return 删除的记录数
     */
    Integer deleteByIds(List<String> ids);

}