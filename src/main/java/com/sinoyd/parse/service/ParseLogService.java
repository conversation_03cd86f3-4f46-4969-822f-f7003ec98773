package com.sinoyd.parse.service;

import com.sinoyd.parse.dto.DtoLog;
import com.sinoyd.frame.service.IBaseJpaService;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;


/**
 * Log操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface ParseLogService extends IBaseJpaService<DtoLog, String> {
    /**
     * 根据日志id获取流程数据、错误日志、
     * @param id 日志id
     * @return Map<String, Object>
     */
    Map<String, Object> getDetails(String id, String gatherCode, String analyzeItemName, String paramName);

    /**
     * 根据日志id下载解析文件
     * @param logId 日志id
     * @return Map<String, Object>
     */
    String download(String logId, HttpServletResponse response);
}