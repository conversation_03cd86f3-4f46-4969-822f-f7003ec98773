package com.sinoyd.parse.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.dto.DtoOrgStream;
import com.sinoyd.parse.dto.DtoStreamDataErrLog;
import com.sinoyd.parse.dto.DtoStreamErrLog;
import com.sinoyd.parse.dto.DtoStreamLog;


/**
 * 解析流日志操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface StreamLogService extends IBaseJpaService<DtoStreamLog, String> {


    /**
     * 按照解析流日志主键id查询源数据流信息
     *
     * @param id 解析流日志主键id
     * @return DtoOrgStream
     */
    DtoOrgStream findSourceStream(String id);

    /**
     * 按照解析流日志主键id查询解析流数据及错误日志信息
     *
     * @param id        按照解析流日志主键id
     * @param paramName 参数名称
     * @return DtoStreamDataErrLog
     */
    DtoStreamDataErrLog findStreamDataErrLog(String id, String paramName);

    /**
     * 按照解析流错误日志主键id查询错误日志详细信息
     *
     * @param id 错误日志主键id
     * @return DtoStreamErrLog
     */
    DtoStreamErrLog findStreamErrLog(String id);

}