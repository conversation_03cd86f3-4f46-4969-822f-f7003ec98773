package com.sinoyd.parse.service;


import com.sinoyd.parse.enums.EnumWebSocketType;
import com.sinoyd.parse.vo.WSConnectionVO;
import com.sinoyd.parse.vo.WSMessageVO;

import javax.websocket.Session;
import java.util.List;
import java.util.Map;

/**
 * WebSocket管理器服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
public interface WebSocketManagerService {
    
    /**
     * 添加WebSocket连接
     *
     * @param session WebSocket会话
     * @param type    连接类型
     * @param userId  用户ID
     * @return WebSocket连接信息
     */
    WSConnectionVO addConnection(Session session, EnumWebSocketType type, String userId);
    
    /**
     * 移除WebSocket连接
     *
     * @param sessionId 会话ID
     * @return 是否移除成功
     */
    boolean removeConnection(String sessionId);
    
    /**
     * 获取WebSocket连接
     *
     * @param sessionId 会话ID
     * @return WebSocket连接信息
     */
    WSConnectionVO getConnection(String sessionId);
    
    /**
     * 向指定类型的所有连接发送消息
     *
     * @param type    连接类型
     * @param message 消息内容
     * @return 发送成功的连接数量
     */
    int sendMessageToType(EnumWebSocketType type, String message);
    
    /**
     * 向指定类型的所有连接发送消息
     *
     * @param type           连接类型
     * @param WSMessageVO WebSocket消息对象
     * @return 发送成功的连接数量
     */
    int sendMessageToType(EnumWebSocketType type, WSMessageVO WSMessageVO);
    
    /**
     * 获取指定类型的连接数量
     *
     * @param type 连接类型
     * @return 连接数量
     */
    int getConnectionCount(EnumWebSocketType type);
    
    /**
     * 获取指定用户的连接数量
     *
     * @param userId 用户ID
     * @return 连接数量
     */
    int getUserConnectionCount(String userId);
    
    /**
     * 获取总连接数量
     *
     * @return 总连接数量
     */
    int getTotalConnectionCount();
    
    /**
     * 获取所有连接统计信息
     *
     * @return 连接统计信息 Map<WebSocketType, Integer>
     */
    Map<EnumWebSocketType, Integer> getConnectionStatistics();
    
    /**
     * 获取指定类型的所有连接
     *
     * @param type 连接类型
     * @return 连接列表
     */
    List<WSConnectionVO> getConnectionsByType(EnumWebSocketType type);
    
    /**
     * 获取指定用户的所有连接
     *
     * @param userId 用户ID
     * @return 连接列表
     */
    List<WSConnectionVO> getConnectionsByUser(String userId);
    
    /**
     * 清理无效连接
     *
     * @return 清理的连接数量
     */
    int cleanInvalidConnections();
    
    /**
     * 更新连接的最后活跃时间
     *
     * @param sessionId 会话ID
     */
    void updateLastActiveTime(String sessionId);
    
    /**
     * 检查连接是否存在
     *
     * @param sessionId 会话ID
     * @return 是否存在
     */
    boolean isConnectionExists(String sessionId);
}
