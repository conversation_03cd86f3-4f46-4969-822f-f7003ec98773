package com.sinoyd.parse.service;

import com.sinoyd.parse.dto.DtoAppFlowData;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.dto.DtoLog;


/**
 * AppFlowData操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface AppFlowDataService extends IBaseJpaService<DtoAppFlowData, String> {


    /**
     * 获取附件上传路径数据
     *
     * @param id 日志流程id
     * @return 日志流程数据
     */
    DtoAppFlowData findAttachPath(String id);
}