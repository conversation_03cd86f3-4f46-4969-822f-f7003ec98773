package com.sinoyd.parse.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.dto.DtoStreamModel;

import java.util.List;


/**
 * 同步采集数据服务操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/07/31
 * @since V100R001
 */
public interface StreamModelService {

    /**
     * 同步采集数据
     * @param streamModels 分页信息对象
     * @return int 同步的记录数
     */
    int syncData(List<DtoStreamModel> streamModels);

}