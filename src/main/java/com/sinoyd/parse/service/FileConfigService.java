package com.sinoyd.parse.service;

import com.sinoyd.parse.dto.DtoDocument;
import com.sinoyd.parse.dto.DtoFileConfig;
import com.sinoyd.frame.service.IBaseJpaService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * FileConfig操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface FileConfigService extends IBaseJpaService<DtoFileConfig, String> {

    /**
     * 方案文件上传
     *
     * @param request
     * @param configId  方案id
     * @return 文件名称
     */
    String upload(HttpServletRequest request, String configId);

    /**
     * 方案文件下载
     *
     * @param configId  方案id
     * @param response 响应对象
     * @return 下载文件路径
     */
    String download(String configId, HttpServletResponse response);

    /**
     * 方案文件下载
     *
     * @param configId  方案id
     * @return List<DtoDocument>
     */
    List<DtoDocument> getAttachInfo(String configId);

    /**
     * 删除方案附件
     *
     * @param configId  方案id
     */
    void deleteAttachInfo(String configId);

    /**
     * 导出解析方案信息
     *
     * @param ids       方案id列表
     * @param response  响应对象
     * @param fileName  文件名称
     */
    void exportConfig(List<String> ids, HttpServletResponse response, String fileName);

    /**
     * 导入解析方案信息
     *
     * @param file 传入的文件
     */
    void importConfig(MultipartFile file, HttpServletResponse response);
}