package com.sinoyd.parse.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.dto.DtoInstrumentConfig;
import com.sinoyd.parse.dto.DtoStreamParamConfig;

import java.util.List;


/**
 * 解析流仪器配置操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface InstrumentConfigService extends IBaseJpaService<DtoInstrumentConfig, String> {

    /**
     * 按照解析流方案主键id查询方案参数配置信息列表
     * @param planId   解析流方案主键id
     * @return List<DtoStreamParamConfig>
     */
    List<DtoStreamParamConfig> findByPlanId(String planId);

    /**
     * 新增解析流方案参数配置
     *
     * @param entity 解析流方案参数信息实体对象
     * @return DtoStreamParamConfig
     */
    DtoStreamParamConfig saveStreamParamConfig(DtoStreamParamConfig entity);

    /**
     * 更新解析流方案参数信息
     *
     * @param entity 解析流方案参数信息实体对象
     * @return DtoStreamParamConfig
     */
    DtoStreamParamConfig updateStreamParamConfig(DtoStreamParamConfig entity);


    /**
     * 按照解析流方案id列表 删除解析流方案信息
     *
     * @param ids 解析流方案信息对象列表
     * @return 删除的记录数
     */
    Integer deleteByIds(List<String> ids);

}