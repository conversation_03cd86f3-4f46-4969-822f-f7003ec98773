package com.sinoyd.parse.service;

import com.sinoyd.parse.dto.DtoFileApp;
import com.sinoyd.frame.service.IBaseJpaService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * FileApp操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface FileAppService extends IBaseJpaService<DtoFileApp, String> {
    /**
     * 根据方案id删除应用配置
     * @param configIds 方案id列表
     */
    void deleteByConfigIds(List<String> configIds);

    /**
     * 上传文件
     * @param appId 应用id
     * @param request 携带文件的请求
     * @return 上传状态
     */
    Boolean upload(String appId, HttpServletRequest request);

    /**
     * 手动执行
     * @return Boolean
     * @param ids
     */
    Boolean parse(List<String> ids);
}