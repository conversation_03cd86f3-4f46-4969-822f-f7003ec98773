package com.sinoyd.parse.service;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface ImportExcelService<T, V> {
    /**
     * 处理导入表格
     *
     * @param file 传入的文件
     * @return List<T>
     */
    List<V> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception;

    /**
     * 导入到数据库
     *
     * @param data 需要导入的数据
     */
    void addData(List<V> data);

    /**
     * 获取文件需要导入的数据
     *
     * @param file 传入的文件
     * @return List
     */
    ExcelImportResult<T> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception;

    /**
     * 获取文件需要导入的数据
     *
     * @param verifyHandler 校验器
     * @param file          传入的文件
     * @param response      响应体
     * @return 导入结果
     */
    default ExcelImportResult<T> getExcelData(IExcelVerifyHandler<T> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }
}
