package com.sinoyd.parse.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.dto.DtoErrorLog;


/**
 * ErrorLog操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface ErrorLogService extends IBaseJpaService<DtoErrorLog, String> {

    /**
     * 根据流程id查询相关错误日志
     * @param parselogId 日志id
     * @return DtoErrorLog
     */
    DtoErrorLog findErrorLog(String parselogId);
}