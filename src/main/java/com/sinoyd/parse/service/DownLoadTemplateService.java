package com.sinoyd.parse.service;

import javax.servlet.http.HttpServletResponse;

/**
 * 导入模板下载接口
 * <AUTHOR>
 * @version V1.0.0 2024/12/09
 * @since V100R001
 */
public interface DownLoadTemplateService {
    /**
     * 验室仪器解析-解析方案配置结果映射导入模板下载
     *
     * @param response      响应流
     * @param fileName      文件名
     */
    void downLoadTemplateResultAlais(HttpServletResponse response, String fileName);


}
