package com.sinoyd.parse.service;

import com.sinoyd.parse.dto.DtoDatas;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.Collection;
import java.util.List;


/**
 * Datas操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface DatasService extends IBaseJpaService<DtoDatas, String> {

    /**
     * 根据样品编号查询
     *
     * @param sampleCodes 样品编号
     * @return 查询结果
     */
    List<DtoDatas> findBySampleCodes(Collection<String> sampleCodes);
}