package com.sinoyd.parse.service;

import com.sinoyd.parse.dto.DtoFileFlow;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * FileFlow操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface FileFlowService extends IBaseJpaService<DtoFileFlow, String> {
    /**
     * 根据解析方案id删除流程配置
     * @param configIds 解析方案id列表
     */
    void deleteByConfigIds(List<String> configIds);
}