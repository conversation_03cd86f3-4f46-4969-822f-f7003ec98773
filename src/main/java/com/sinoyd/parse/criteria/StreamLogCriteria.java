package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


/**
 * 解析流日志查询条件
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StreamLogCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 解析开始时间
     */
    private String parseTimeStart;

    /**
     * 解析结束时间
     */
    private String parseTimeEnd;

    /**
     * 关键字（仪器名称/编号）
     */
    private String instrumentNameCode;

    /**
     * 解析状态（解析状态 1：解析成功 2：解析失败）
     */
    private String parseStatus;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotEmpty(this.parseTimeStart)) {
            Date startDate = DateUtil.stringToDate(parseTimeStart, DateUtil.FULL);
            condition.append(" and a.parseTime >= :parseTimeStart");
            values.put("parseTimeStart", startDate);
        }
        if (StringUtil.isNotEmpty(this.parseTimeEnd)) {
            Date endDate = DateUtil.stringToDate(parseTimeEnd, DateUtil.FULL);
            condition.append(" and a.parseTime <= :parseTimeEnd");
            values.put("parseTimeEnd", endDate);
        }
        if (StringUtil.isNotEmpty(this.instrumentNameCode)) {
            condition.append(" and (b.instrumentName like :instName or b.instrumentCode like :instCode)");
            values.put("instName", "%" + this.instrumentNameCode + "%");
            values.put("instCode", "%" + this.instrumentNameCode + "%");
        }
        if (StringUtil.isNotEmpty(this.parseStatus)) {
            condition.append(" and a.parseStatus = :parseStatus");
            values.put("parseStatus", this.parseStatus);
        }
        condition.append(" and a.appId = b.id ");
        condition.append(" and a.isDeleted = 0 ");
        condition.append(" and b.isDeleted = 0 ");
        condition.append(" order by a.parseTime desc ");
        return condition.toString();
    }
}