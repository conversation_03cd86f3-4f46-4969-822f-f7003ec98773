package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;



/**
 * FileFlow查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileFlowCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 方案id
    */
    private String planId;
    /**
     * 流程名称
     */
    private String flowName;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.planId)) {
            condition.append(" and planId = :planId");
            values.put("planId", this.planId);
        }
        if (StringUtil.isNotEmpty(this.flowName)) {
            condition.append(" and flowName like :flowName");
            values.put("flowName", "%" + this.flowName + "%");
        }
        return condition.toString();
    }
}