package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;



/**
 * Log查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParseLogCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 应用id
    */
    private String appId;
    /**
     * 仪器编码,仪器名称
     */
    private String key;
    /**
     * 起始时间
     */
    private String fromTime;
    /**
     * 结束时间
     */
    private String toTime;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 解析状态
     */
    private String parseStatus;
    /**
     * 解析类型
     */
    private String parseType;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.appId = b.id ");
        String orgId = PrincipalContextUser.getPrincipal().getOrgId();
        if (StringUtil.isNotEmpty(orgId)) {
            condition.append(" and a.orgId = :orgId ");
            values.put("orgId", orgId);
        }
        if (StringUtil.isNotEmpty(this.appId)) {
            condition.append(" and appId = :appId");
            values.put("appId", this.appId);
        }
        if (StringUtil.isNotEmpty(parseType)) {
            condition.append(" and a.parseType = :parseType ");
            values.put("parseType", this.parseType);
        }
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and ( LOWER(b.instrumentCode) like :key or LOWER(b.instrumentName) like :key ) ");
            values.put("key", "%" + this.key.toLowerCase() + "%");
        }
        if (StringUtil.isNotEmpty(this.parseStatus)) {
            condition.append(" and parseStatus = :parseStatus");
            values.put("parseStatus", this.parseStatus);
        }
        if (StringUtil.isNotEmpty(this.fileName)) {
            condition.append(" and LOWER(fileOrgName) like :fileOrgName ");
            values.put("fileOrgName", "%" + this.fileName.toLowerCase() + "%");
        }
        if (StringUtil.isNotEmpty(this.fromTime)) {
            condition.append(" and beginTime >= :fromTime");
            values.put("fromTime", this.fromTime);
        }
        if (StringUtil.isNotEmpty(this.toTime)) {
            condition.append(" and beginTime <= :toTime");
            values.put("toTime", this.toTime);
        }
        return condition.toString();
    }
}