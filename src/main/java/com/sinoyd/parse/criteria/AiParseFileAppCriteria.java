package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI仪器解析应用查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "AI仪器解析应用查询条件")
public class AiParseFileAppCriteria extends BaseCriteria {

    /**
     * 查询关键字（仪器名称，仪器编号）
     */
    private String key;

    /**
     * 解析类型，枚举管理{@link com.sinoyd.parse.enums.EnumParseType}
     */
    private Integer parseType;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        //查询关键字
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and a.instrumentName like :key or a.instrumentCode like :key) ");
            values.put("key", "%" + this.key.toLowerCase() + "%");
        }
        //解析类型
        if (StringUtil.isNotNull(this.parseType)) {
            condition.append(" and a.parseType = :parseType ");
            values.put("parseType", this.parseType);
        }
        return condition.toString();
    }
}
