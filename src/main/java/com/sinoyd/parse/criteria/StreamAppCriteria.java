package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 解析流应用查询条件
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StreamAppCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关键字(应用名称，仪器名称，仪器编号，方案名称)
     */
    private String appInstConfigName;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.appInstConfigName)) {
            condition.append("and (a.appName like :appName or a.instrumentName like :instName or a.instrumentCode like :instCode)");
            values.put("appName", "%" + this.appInstConfigName + "%");
            values.put("instName", "%" + this.appInstConfigName + "%");
            values.put("instCode", "%" + this.appInstConfigName + "%");
        }
        condition.append(" and a.planId = b.id ");
        condition.append(" and a.isDeleted = 0 ");
        condition.append(" and b.isDeleted = 0 ");
//        condition.append(" order by a.appName");
        return condition.toString();
    }
}