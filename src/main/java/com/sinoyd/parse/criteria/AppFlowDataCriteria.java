package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;



/**
 * AppFlowData查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppFlowDataCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    private String appId;
    /**
     * 起始时间
     */
    private String fromTime;
    /**
     * 结束时间
     */
    private String toTime;
    /**
     * 文件名
     */
    private String fileName;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.parselogId = b.id ");
        if (StringUtil.isNotEmpty(this.appId)) {
            condition.append(" and b.appId = :appId");
            values.put("appId", this.appId);
        }
        if (StringUtil.isNotEmpty(this.fileName)) {
            condition.append(" and LOWER(b.fileOrgName) like :fileOrgName ");
            values.put("fileOrgName", "%" + this.fileName.toLowerCase() + "%");
        }
        if (StringUtil.isNotEmpty(this.fromTime)) {
            condition.append(" and parseTime >= :fromTime");
            values.put("fromTime", this.fromTime);
        }
        if (StringUtil.isNotEmpty(this.toTime)) {
            condition.append(" and parseTime <= :toTime");
            values.put("toTime", this.toTime);
        }
        return condition.toString();
    }
}