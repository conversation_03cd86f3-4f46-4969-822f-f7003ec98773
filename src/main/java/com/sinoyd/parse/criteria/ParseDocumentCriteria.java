package com.sinoyd.parse.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仪器解析文件管理查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "仪器解析文件管理查询条件")
public class ParseDocumentCriteria extends BaseCriteria {
    @Override
    public String getCondition() {
        return "";
    }
}
