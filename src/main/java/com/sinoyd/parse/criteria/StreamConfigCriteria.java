package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 解析流方案配置查询条件
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StreamConfigCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 方案名称
     */
    private String planName;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.planName)) {
            condition.append("and planName like :planName");
            values.put("planName", "%" + this.planName + "%");
        }
        condition.append(" and a.isDeleted = 0 ");
        condition.append(" order by a.planName ");
        return condition.toString();
    }
}