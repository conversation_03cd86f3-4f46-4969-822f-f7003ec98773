package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


/**
 * 移动端实时查询现场解析数据查询条件
 * <AUTHOR>
 * @version V1.0.0 2021/07/30
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StreamDataMobileCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据采集开始时间
     */
    private String getTimeStart;

    /**
     * 数据采集结束时间
     */
    private String getTimeEnd;

    /**
     * 仪器关键字（仪器名称/编号）
     */
    private String instrumentName;

    /**
     * 关键字（参数名称/别名/文件编号）
     */
    private String paramNameSerialNumber;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.getTimeStart)) {
            Date startDate = DateUtil.stringToDate(getTimeStart, DateUtil.FULL);
            condition.append(" and b.getTime >= :getTimeStart");
            values.put("getTimeStart", startDate);
        }
        if (StringUtil.isNotEmpty(this.getTimeEnd)) {
            Date endDate = DateUtil.stringToDate(getTimeEnd, DateUtil.FULL);
            condition.append(" and b.getTime <= :getTimeEnd");
            values.put("getTimeEnd", endDate);
        }
        if (StringUtil.isNotEmpty(this.instrumentName)) {
            condition.append(" and (a.instrumentName like :instName or a.instrumentCode like :instCode)");
            values.put("instName", "%" + this.instrumentName + "%");
            values.put("instCode", "%" + this.instrumentName + "%");
        }
        if (StringUtil.isNotEmpty(this.paramNameSerialNumber)) {
            condition.append(" and (a.paramName like :paramName or a.paramAlias like :paramAlias or a.serialNumber like :serialNumber)");
            values.put("paramName", "%" + this.paramNameSerialNumber + "%");
            values.put("paramAlias", "%" + this.paramNameSerialNumber + "%");
            values.put("serialNumber", "%" + this.paramNameSerialNumber + "%");
        }
        condition.append(" and a.parseStreamLogId = b.id ");
        condition.append(" and a.isDeleted = 0 ");
        condition.append(" and b.parseStatus = '1' ");
        condition.append(" and b.isDeleted = 0 ");
        condition.append(" order by b.getTime desc ");
        return condition.toString();
    }
}