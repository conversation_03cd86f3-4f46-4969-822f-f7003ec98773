package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * AI仪器解析配置查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/07
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AiInstrumentConfigCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器类型
     */
    private String instrumentType;

    /**
     * 关键字搜索（仪器名称、仪器编号、所属单位）
     */
    private String keyword;

    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();

        // 仪器类型精确查询
        if (StringUtil.isNotEmpty(this.instrumentType)) {
            condition.append(" and a.instrumentType = :instrumentType ");
            values.put("instrumentType", this.instrumentType);
        }

        // 关键字搜索（仪器名称、仪器编号、所属单位）
        if (StringUtil.isNotEmpty(this.keyword)) {
            condition.append(" and a.instrumentName like :keyword  ");
            values.put("keyword", "%" + this.keyword.toLowerCase() + "%");
        }

        return condition.toString();
    }
}
