package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


/**
 * 现场解析数据查询条件
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StreamDataCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据解析开始时间
     */
//    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private String parseTimeStart;

    /**
     * 数据解析结束时间
     */
//    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private String parseTimeEnd;

    /**
     * 参数关键字（参数名称/别名）
     */
    private String paramNameAlias;

    /**
     * 仪器关键字（仪器名称/编号）
     */
    private String instrumentNameCode;

    /**
     * 参数类型（参数类型 （ 0: 所有 1：样品参数 2：公共参数 3：现场值 4：电子天平）
     */
    private Integer paramType;

    /**
     * 文件名称
     */
    private String fileName;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotEmpty(this.parseTimeEnd)) {
            Date endDate = DateUtil.stringToDate(parseTimeEnd, DateUtil.FULL);
            condition.append(" and b.parseTime <= :parseTimeEnd");
            values.put("parseTimeEnd", endDate);
        }
        if (StringUtil.isNotEmpty(this.parseTimeStart)) {
            Date startDate = DateUtil.stringToDate(parseTimeStart, DateUtil.FULL);
            condition.append(" and b.parseTime >= :parseTimeStart");
            values.put("parseTimeStart", startDate);
        }
        if (StringUtil.isNotEmpty(this.instrumentNameCode)) {
            condition.append(" and (a.instrumentName like :instName or a.instrumentCode like :instCode)");
            values.put("instName", "%" + this.instrumentNameCode + "%");
            values.put("instCode", "%" + this.instrumentNameCode + "%");
        }
        if (StringUtil.isNotEmpty(this.paramNameAlias)) {
            condition.append(" and (a.paramName like :paramName or a.paramAlias like :paramAlias)");
            values.put("paramName", "%" + this.paramNameAlias + "%");
            values.put("paramAlias", "%" + this.paramNameAlias + "%");
        }
        if (StringUtil.isNotNull(this.paramType) && this.paramType != 0) {
            condition.append(" and a.paramType = :paramType");
            values.put("paramType", this.paramType);
        }
        if (StringUtil.isNotEmpty(this.fileName)) {
            condition.append(" and a.serialNumber like :fileName ");
            values.put("fileName", "%" + this.fileName + "%");
        }
        condition.append(" and a.parseStreamLogId = b.id ");
        condition.append(" and a.appId = c.id ");
        condition.append(" and a.isDeleted = 0 ");
        condition.append(" and b.parseStatus = '1'");
        condition.append(" and b.isDeleted = 0 ");
        condition.append(" and c.isDeleted = 0 ");
        condition.append(" order by a.parseDataTime desc, a.serialNumber ");
        return condition.toString();
    }
}