package com.sinoyd.parse.vo;

import com.sinoyd.parse.enums.EnumMessagePriority;
import com.sinoyd.parse.enums.EnumMessageType;
import com.sinoyd.parse.enums.EnumWebSocketType;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * WebSocket消息实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
@Data
public class WSMessageVO {
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 消息类型
     */
    private EnumMessageType enumMessageType;
    
    /**
     * WebSocket连接类型
     */
    private EnumWebSocketType enumWebSocketType;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息标题
     */
    private String title;
    
    /**
     * 发送者ID
     */
    private String senderId;
    
    /**
     * 发送者名称
     */
    private String senderName;
    
    /**
     * 目标用户ID列表（为空表示广播）
     */
    private List<String> targetUserIds;
    
    /**
     * 目标机构ID列表
     */
    private List<String> targetOrgIds;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 发送时间
     */
    private Date sendTime;
    
    /**
     * 消息优先级
     */
    private EnumMessagePriority priority;
    
    /**
     * 是否需要确认
     */
    private Boolean needConfirm;
    
    /**
     * 扩展数据
     */
    private String extendData;
    
    /**
     * 默认构造函数
     */
    public WSMessageVO() {
        this.createTime = new Date();
        this.priority = EnumMessagePriority.NORMAL;
        this.needConfirm = false;
    }
    
    /**
     * 构造函数
     *
     * @param enumWebSocketType WebSocket连接类型
     * @param enumMessageType   消息类型
     * @param content       消息内容
     */
    public WSMessageVO(EnumWebSocketType enumWebSocketType, EnumMessageType enumMessageType, String content) {
        this();
        this.enumWebSocketType = enumWebSocketType;
        this.enumMessageType = enumMessageType;
        this.content = content;
    }
    
    /**
     * 创建文本消息
     *
     * @param enumWebSocketType WebSocket连接类型
     * @param content       消息内容
     * @return WebSocket消息
     */
    public static WSMessageVO createTextMessage(EnumWebSocketType enumWebSocketType, String content) {
        return new WSMessageVO(enumWebSocketType, EnumMessageType.TEXT, content);
    }

    /**
     * 创建JSON消息
     *
     * @param enumWebSocketType WebSocket连接类型
     * @param content       JSON内容
     * @return WebSocket消息
     */
    public static WSMessageVO createJsonMessage(EnumWebSocketType enumWebSocketType, String content) {
        return new WSMessageVO(enumWebSocketType, EnumMessageType.JSON, content);
    }
}
