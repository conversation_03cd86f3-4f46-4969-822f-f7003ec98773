package com.sinoyd.parse.vo;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.parse.enums.EnumConnectionStatus;
import com.sinoyd.parse.enums.EnumWebSocketType;
import lombok.Data;

import javax.websocket.Session;
import java.util.Date;

/**
 * WebSocket连接信息
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
@Data
public class WSConnectionVO {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * WebSocket会话
     */
    private Session session;

    /**
     * 连接类型
     */
    private EnumWebSocketType type;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 连接时间
     */
    private Date connectTime;

    /**
     * 最后活跃时间
     */
    private Date lastActiveTime;

    /**
     * 客户端IP地址
     */
    private String clientIp;

    /**
     * 用户代理信息
     */
    private String userAgent;

    /**
     * 连接状态
     */
    private EnumConnectionStatus status;

    /**
     * 扩展属性
     */
    private String extendData;

    /**
     * 默认构造函数
     */
    public WSConnectionVO() {
        this.connectTime = new Date();
        this.lastActiveTime = new Date();
        this.status = EnumConnectionStatus.CONNECTED;
    }

    /**
     * 构造函数
     *
     * @param session WebSocket会话
     * @param type    连接类型
     * @param userId  用户ID
     */
    public WSConnectionVO(Session session, EnumWebSocketType type, String userId) {
        this();
        this.sessionId = session.getId();
        this.session = session;
        this.type = type;
        this.userId = userId;
    }

    /**
     * 更新最后活跃时间
     */
    public void updateLastActiveTime() {
        this.lastActiveTime = new Date();
    }

    /**
     * 检查连接是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return session != null && session.isOpen() && status == EnumConnectionStatus.CONNECTED;
    }

    /**
     * 关闭连接
     */
    public void close() {
        this.status = EnumConnectionStatus.DISCONNECTED;
        if (session != null && session.isOpen()) {
            try {
                session.close();
            } catch (Exception e) {
                throw new BaseException("关闭WebSocket连接失败");
            }
        }
    }
}
