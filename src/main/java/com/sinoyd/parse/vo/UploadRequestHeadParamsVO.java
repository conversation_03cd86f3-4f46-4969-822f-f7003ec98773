package com.sinoyd.parse.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 附件上传请求头参数
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Data
@ApiModel(description = "附件上传请求头参数")
public class UploadRequestHeadParamsVO {

    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型")
    private String docTypeId;

    /**
     * 文件类型名称
     */
    @ApiModelProperty("文件类型名称")
    private String docTypeName;

    /**
     * 对象id
     */
    @ApiModelProperty("对象id")
    private String objectId;

    /**
     * 上传路径
     */
    @ApiModelProperty("上传路径")
    private String uploadPath;

    /**
     * 是否覆盖上传
     */
    @ApiModelProperty("是否覆盖上传")
    private Boolean isCoverFile;

    /**
     * 上传的文件
     */
    @ApiModelProperty("上传的文件")
    private List<MultipartFile> files;

    /**
     * 构造方法
     */
    public UploadRequestHeadParamsVO() {
    }

    /**
     * 通过请求头构造
     *
     * @param request 请求头
     */
    public UploadRequestHeadParamsVO(HttpServletRequest request) {
        this.docTypeId = request.getParameter("docTypeId");
        this.docTypeName = request.getParameter("docTypeName");
        this.objectId = request.getParameter("objectId");
        this.uploadPath = request.getParameter("uploadPath");
        this.files = ((MultipartHttpServletRequest) request).getFiles("files");
        this.isCoverFile = Boolean.valueOf(request.getParameter("isCoverFile"));
    }
}
