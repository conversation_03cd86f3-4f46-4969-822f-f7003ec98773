package com.sinoyd.parse.vo;

import lombok.Data;

import java.util.List;

@Data
public class OcrDataContainerVO {

//    /**
//     * ocr匹配结果数据
//     */
//    private List<DtoOcrConfigParamData> dataList;
//
//    /**
//     * ocr步骤
//     */
//    private String ocrParse;
//
//    /**
//     * ai回答结果
//     */
//    private String aiAnswer;
//
//    /**
//     * 识别记录id
//     */
//    private String recordId;
}
