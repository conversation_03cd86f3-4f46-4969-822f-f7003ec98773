package com.sinoyd.parse.constants;

/**
 * 仪器解析常量
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/12/09
 */
public interface ParseConstants {

    /**
     * 导入的常量
     */
    interface ImportConstants{

        /**
         * 污染源客户类型字典编码
         */
        String LIM_POLLUTION_SOURCE_TYPE = "LIM_PollutionSourceType";
        /**
         * 评价标准类型（国标）
         */
        String EVALUATION_TYPE_GB = "BASE_EvaluateType_GB";

        /**
         * 环境质量点位类型的字典编码
         */
        String FIXED_POINT_TYPE_HJ = "LIM_EnvQualityPointType";

        /**
         * 污染源点位类型的字典编码
         */
        String FIXED_POINT_TYPE_WR = "LIM_PollutionPointType";

        /**
         * 控制等级的字典编码
         */
        String CONTROL_LEVEL = "LIM_ControlLevel";

        /**
         * 英文评价标准等级分隔符号
         */
        String ENGLISH_SPLIT_CHAR = "##";

        /**
         * 中文评价标准等级分隔符号
         */
        String CHINESE_SPLIT_CHAR = "##";

        /**
         * 表1的key
         */
        String FIRST_SHEET_NAME = "firstName";

        /**
         * 表2的key
         */
        String SECOND_SHEET_NAME = "secondName";

        /**
         * 默认次数（例行任务点位导入，周期，次数）
         */
        Integer DEFAULT_ORDER_NUM = 1;
    }
}
