package com.sinoyd.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 源数据流实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "OrgStream")
@Data
@EntityListeners(AuditingEntityListener.class)
public class OrgStream implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public OrgStream() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column
    private String id = UUIDHelper.NewID();

    /**
     * 应用id
     */
    @ApiModelProperty("应用id")
    private String appId;

    /**
     * 方案id
     */
    @ApiModelProperty("方案id")
    private String planId;

    /**
     * 解析日志主键id
     */
    @ApiModelProperty("解析日志主键")
    private String parseStreamLogId;

    /**
     * 仪器Id
     */
    @Column
    @ApiModelProperty("仪器Id")
    private String instrumentId;

    /**
     * 仪器名称
     */
    @Column
    @ApiModelProperty("仪器名称")
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Column
    @ApiModelProperty("仪器编号")
    private String instrumentCode;


    /**
     * 源数据流内容
     */
    @ApiModelProperty("源数据流内容")
    private String streamContent;



    /**
     * 组织机构Id
     */
    @Column
    @ApiModelProperty("组织机构Id")
    private String orgId;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建日期
     */
    @CreatedDate
    @ApiModelProperty("创建日期")
    private Date createDate;

    /**
     * 修改人
     */
    @Column
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改日期
     */
    @LastModifiedDate
    @ApiModelProperty("修改日期")
    private Date modifyDate;

}