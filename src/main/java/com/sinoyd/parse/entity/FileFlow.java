package com.sinoyd.parse.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * FileFlow实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="FileFlow")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class FileFlow implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  FileFlow() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 流程名称
    */
    @Column(length=25)
    @ApiModelProperty("流程名称")
    @Length(max = 25, message = "流程名称长度不能超过25")
	private String flowName;
    
    /**
    * 方案Id
    */
    @Column(length=25)
    @ApiModelProperty("方案Id")
	private String planId;
    
    /**
    * 解析类型(1:样品编号，2:分析项目名称，3:参数 4:其他)
    */
    @Column(length=25)
    @ApiModelProperty("解析类型(1:样品编号，2:分析项目名称，3:参数 4:其他)")
	private String parseType;
    
    /**
    * 步骤编号
    */
    @ApiModelProperty("步骤编号")
	private Integer stepNum;
    
    /**
    * 特殊类(处理特殊解析的类)
    */
    @Column(length=25)
    @ApiModelProperty("特殊类(处理特殊解析的类)")
    @Length(max = 25, message = "特殊类长度不能超过25")
	private String specialClass;
    
    /**
    * 是否使用上一步数据
    */
    @Column
    @ApiModelProperty("是否使用上一步数据")
	private Boolean useLastData;
    
    /**
    * 使用方式(1:拼接 2:替换)
    */
    @Column(length=25)
    @ApiModelProperty("使用方式(1:拼接 2:替换)")
	private String useType;
    
    /**
    * 组织机构Id
    */
    @Column(length=25)
    @ApiModelProperty("组织机构Id")
	private String orgId;
    
    /**
    * 是否删除
    */
    @ApiModelProperty("是否删除")
	private Boolean isDeleted= false;
    
    /**
    * 创建人
    */
    @Column(length=25)
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建日期
    */
    @CreatedDate
    @ApiModelProperty("创建日期")
	private Date createDate;
    
    /**
    * 修改人
    */
    @Column(length=25)
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改日期
    */
    @LastModifiedDate
    @ApiModelProperty("修改日期")
	private Date modifyDate;
    
 }