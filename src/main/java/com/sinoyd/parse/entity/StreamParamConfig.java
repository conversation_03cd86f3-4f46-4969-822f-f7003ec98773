package com.sinoyd.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 解析流方案参数配置实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "StreamParamConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class StreamParamConfig implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public StreamParamConfig() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column
    private String id = UUIDHelper.NewID();

    /**
     * 方案id
     */
    @Column
    @ApiModelProperty("方案id")
    private String planId;

    /**
     * 参数名称
     */
    @Column
    @ApiModelProperty("参数名称")
    private String paramName;

    /**
     * 参数别名
     */
    @Column
    @ApiModelProperty("参数别名")
    private String paramAlias;

    /**
     * 参数类型（1：样品参数2：公共参数3：现场值）
     */
    @ApiModelProperty("参数类型（1：样品参数2：公共参数3：现场值）")
    private Integer paramType;

    /**
     * 正则表达式（开始位置）---文本
     */
    @ApiModelProperty("正则表达式（开始位置）")
    @Column(name = "eigenStart")
    private String eiGenStart;

    /**
     * 正则表达式（结束位置）---文本
     */
    @ApiModelProperty("正则表达式（结束位置）")
    @Column(name = "eigenEnd")
    private String eiGenEnd;

    /**
     * 是否倍率转换
     */
    @ApiModelProperty("是否倍率转换")
    private Boolean rateConvert;

    /**
     * 倍率
     */
    @ApiModelProperty("倍率")
    private String rateNum;

    /**
     * 是否保留精度修正
     */
    @ApiModelProperty("是否保留精度修正")
    private Boolean checkPrecision;

    /**
     * 保留有效位数
     */
    @ApiModelProperty("保留有效位数")
    private Integer effectDecimal;

    /**
     * 保留小数位数
     */
    @ApiModelProperty("保留小数位数")
    private Integer mostDecimal;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    @Column
    private String unit;

    /**
     * 排序号
     */
    @ApiModelProperty("排序号")
    private Integer orderNum;


    /**
     * 组织机构Id
     */
    @Column
    @ApiModelProperty("组织机构Id")
    private String orgId;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建日期
     */
    @CreatedDate
    @ApiModelProperty("创建日期")
    private Date createDate;

    /**
     * 修改人
     */
    @Column
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改日期
     */
    @LastModifiedDate
    @ApiModelProperty("修改日期")
    private Date modifyDate;

}