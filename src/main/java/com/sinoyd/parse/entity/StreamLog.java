package com.sinoyd.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 解析流日志实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "StreamLog")
@Data
@EntityListeners(AuditingEntityListener.class)
public class StreamLog implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public StreamLog() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column
    private String id = UUIDHelper.NewID();

    /**
     * 流水号
     */
    @Column
    @ApiModelProperty("流水号")
    private String serialNumber;

    /**
     * 应用id
     */
    @ApiModelProperty("应用id")
    private String appId;

    /**
     * 采集时间
     */
    @ApiModelProperty("采集时间")
    private Date getTime;

    /**
     * 上传时间
     */
    @ApiModelProperty("上传时间")
    private Date postTime;

    /**
     * 解析时间
     */
    @ApiModelProperty("解析时间")
    private Date parseTime;

    /**
     * 解析状态
     */
    @Column
    @ApiModelProperty("解析状态")
    private String parseStatus;

    /**
     * 日志内容
     */
    @ApiModelProperty("日志内容")
    private String logContent;

    /**
     * 组织机构Id
     */
    @Column
    @ApiModelProperty("组织机构Id")
    private String orgId;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建日期
     */
    @CreatedDate
    @ApiModelProperty("创建日期")
    private Date createDate;

    /**
     * 修改人
     */
    @Column
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改日期
     */
    @LastModifiedDate
    @ApiModelProperty("修改日期")
    private Date modifyDate;

}