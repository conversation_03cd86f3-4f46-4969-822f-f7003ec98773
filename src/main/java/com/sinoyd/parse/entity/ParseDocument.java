package com.sinoyd.parse.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;

import javax.persistence.*;

import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;

import java.util.Date;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.validation.constraints.NotNull;

/**
 * 仪器解析文件管理实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@MappedSuperclass
@ApiModel(description = "ParseDocument")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ParseDocument implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ParseDocument() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 对象id
     */
    @Column(length = 50)
    @ApiModelProperty("对象id")
    @Length(max = 50, message = "对象id长度不能超过50")
    private String objectId;

    /**
     * 文件夹名称
     */
    @Column(length = 255)
    @ApiModelProperty("文件夹名称")
    @Length(max = 255, message = "文件夹名称长度不能超过255")
    private String folderName;

    /**
     * 文件名称
     */
    @Column(length = 255)
    @ApiModelProperty("文件名称")
    @Length(max = 255, message = "文件名称长度不能超过255")
    private String filename;

    /**
     * 物理文件名称
     */
    @Column(length = 255)
    @ApiModelProperty("物理文件名称")
    @Length(max = 255, message = "物理文件名称长度不能超过255")
    private String physicalName;

    /**
     * 文件路径
     */
    @Column(length = 500)
    @ApiModelProperty("文件路径")
    @Length(max = 500, message = "文件路径长度不能超过500")
    private String path;

    /**
     * 文件类型
     */
    @Column(length = 50)
    @ApiModelProperty("文件类型")
    @Length(max = 50, message = "文件类型长度不能超过50")
    private String docTypeId;

    /**
     * 文件类型名称
     */
    @Column(length = 255)
    @ApiModelProperty("文件类型名称")
    @Length(max = 255, message = "文件类型名称长度不能超过255")
    private String docTypeName;

    /**
     * 文件大小
     */
    @Column
    @ApiModelProperty("文件大小")
    @NotNull(message = "文件大小不能为空")
    private Integer docSize;

    /**
     * 文件后缀
     */
    @Column(length = 50)
    @ApiModelProperty("文件后缀")
    @Length(max = 50, message = "文件后缀长度不能超过50")
    private String docSuffix;

    /**
     * 排序值
     */
    @Column
    @ApiModelProperty("排序值")
    @NotNull(message = "排序值不能为空")
    private Integer orderNum;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    @Length(max = 1000, message = "备注长度不能超过1000")
    private String remark;

    /**
     * 上传人id
     */
    @Column(length = 50)
    @ApiModelProperty("上传人id")
    @Length(max = 50, message = "上传人id长度不能超过50")
    private String uploadPersonId;

    /**
     * 上传人名称
     */
    @Column(length = 50)
    @ApiModelProperty("上传人名称")
    @Length(max = 50, message = "上传人名称长度不能超过50")
    private String uploadPerson;

    /**
     * 组织机构Id
     */
    @Column(length = 50)
    @ApiModelProperty("组织机构Id")
    private String orgId;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column(length = 50)
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 更新人
     */
    @Column(length = 50)
    @LastModifiedBy
    @ApiModelProperty("更新人")
    private String modifier;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @ApiModelProperty("更新时间")
    private Date modifyDate;
}
