package com.sinoyd.parse.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import com.sinoyd.frame.base.entity.BaseEntity;

import javax.persistence.*;

import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;

import java.util.Date;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * AI仪器解析配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/07
 **/
@MappedSuperclass
@ApiModel(description = "AiInstrumentConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class AiInstrumentConfig implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public AiInstrumentConfig() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 仪器类型：1-现场仪器，2-实验室仪器
     */
    @Column(length = 20)
    @ApiModelProperty("仪器类型：1-现场仪器，2-实验室仪器")
    @Length(max = 20, message = "仪器类型长度不能超过20")
    private String instrumentType;

    /**
     * 解析文件存放目录
     */
    @Column(length = 200)
    @ApiModelProperty("解析文件存放目录")
    private String parseFolder;

    /**
     * 仪器名称
     */
    @Column(length = 200)
    @ApiModelProperty("仪器名称")
    @Length(max = 200, message = "仪器名称长度不能超过200")
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Column(length = 100)
    @ApiModelProperty("仪器编号")
    @Length(max = 100, message = "仪器编号长度不能超过100")
    private String instrumentCode;

    /**
     * 所属单位
     */
    @Column(length = 200)
    @ApiModelProperty("所属单位")
    @Length(max = 200, message = "所属单位长度不能超过200")
    private String belongUnit;

    /**
     * 提示词内容
     */
    @Column(columnDefinition = "TEXT")
    @ApiModelProperty("提示词内容")
    private String promptText;

    /**
     * 组织机构Id
     */
    @Column(length = 50)
    @ApiModelProperty("组织机构Id")
    @Length(max = 50, message = "组织机构Id长度不能超过50")
    private String orgId;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column(length = 50)
    @CreatedBy
    @ApiModelProperty("创建人")
    @Length(max = 50, message = "创建人长度不能超过50")
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 更新人
     */
    @Column(length = 50)
    @LastModifiedBy
    @ApiModelProperty("更新人")
    @Length(max = 50, message = "更新人长度不能超过50")
    private String modifier;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @ApiModelProperty("更新时间")
    private Date modifyDate;
}
