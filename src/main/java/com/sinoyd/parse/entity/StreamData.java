package com.sinoyd.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 解析流数据实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "StreamData")
@Data
@EntityListeners(AuditingEntityListener.class)
public class StreamData implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public StreamData() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column
    private String id = UUIDHelper.NewID();

    /**
     * 应用id
     */
    @ApiModelProperty("应用id")
    private String appId;

    /**
     * 方案id
     */
    @ApiModelProperty("方案id")
    private String planId;

    /**
     * 解析日志主键id
     */
    @ApiModelProperty("解析日志主键")
    private String parseStreamLogId;

    /**
     * 流水号
     */
    @Column
    @ApiModelProperty("流水号")
    private String serialNumber;

    /**
     * 仪器Id
     */
    @Column
    @ApiModelProperty("仪器Id")
    private String instrumentId;

    /**
     * 仪器名称
     */
    @Column
    @ApiModelProperty("仪器名称")
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Column
    @ApiModelProperty("仪器编号")
    private String instrumentCode;

    /**
     * 参数名称
     */
    @Column
    @ApiModelProperty("参数名称")
    private String paramName;

    /**
     * 参数别名
     */
    @Column
    @ApiModelProperty("参数别名")
    private String paramAlias;

    /**
     * 参数类型 （1：样品参数 2：公共参数 3：现场值 4：电子天平
     */
    @ApiModelProperty("参数类型 （1：样品参数 2：公共参数 3：现场值 4：电子天平")
    private Integer paramType;

    /**
     * 参数值
     */
    @Column
    @ApiModelProperty("参数值")
    private String value;

    /**
     * 单位
     */
    @Column
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 数据解析时间
     */
    @ApiModelProperty("数据解析时间")
    private Date parseDataTime;

    /**
     * 组织机构Id
     */
    @Column
    @ApiModelProperty("组织机构Id")
    private String orgId;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建日期
     */
    @CreatedDate
    @ApiModelProperty("创建日期")
    private Date createDate;

    /**
     * 修改人
     */
    @Column
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改日期
     */
    @LastModifiedDate
    @ApiModelProperty("修改日期")
    private Date modifyDate;

    /**
     * 预留值1
     */
    @Column
    @ApiModelProperty("预留值1")
    private String extend1;

    /**
     * 预留值2
     */
    @Column
    @ApiModelProperty("预留值2")
    private String extend2;

    /**
     * 预留值3
     */
    @Column
    @ApiModelProperty("预留值3")
    private String extend3;

    /**
     * 预留值4
     */
    @Column
    @ApiModelProperty("预留值4")
    private String extend4;

    /**
     * 预留值5
     */
    @Column
    @ApiModelProperty("预留值5")
    private String extend5;
}