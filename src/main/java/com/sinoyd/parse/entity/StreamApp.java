package com.sinoyd.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 解析流应用实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "StreamApp")
@Data
@EntityListeners(AuditingEntityListener.class)
public class StreamApp implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public StreamApp() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    public StreamApp(String appName, String instrumentId, String instrumentName, String instrumentCode, String manufacturer,
                     String planId, String instrumentConfigId) {
        this.appName = appName;
        this.instrumentId = instrumentId;
        this.instrumentName = instrumentName;
        this.instrumentCode = instrumentCode;
        this.manufacturer = manufacturer;
        this.planId = planId;
        this.instrumentConfigId = instrumentConfigId;
    }

    /**
     * 主键id
     */
    @Id
    @Column
    private String id = UUIDHelper.NewID();

    /**
     * 应用名称
     */
    @Column
    @ApiModelProperty("应用名称")
    private String appName;

    /**
     * 仪器Id
     */
    @Column
    @ApiModelProperty("仪器Id")
    private String instrumentId;

    /**
     * 仪器名称
     */
    @Column
    @ApiModelProperty("仪器名称")
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Column
    @ApiModelProperty("仪器编号")
    private String instrumentCode;

    /**
     * 生产厂商
     */
    @Column(name= "instrumentManufacturer")
    @ApiModelProperty("生产厂商")
    private String manufacturer;

    /**
     * 流方案Id
     */
    @Column
    @ApiModelProperty("流方案Id")
    private String planId;

    /**
     * 仪器配置Id
     */
    @Column
    @ApiModelProperty("仪器配置Id")
    private String instrumentConfigId;

    /**
     * 组织机构Id
     */
    @Column
    @ApiModelProperty("组织机构Id")
    private String orgId;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建日期
     */
    @CreatedDate
    @ApiModelProperty("创建日期")
    private Date createDate;

    /**
     * 修改人
     */
    @Column
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改日期
     */
    @LastModifiedDate
    @ApiModelProperty("修改日期")
    private Date modifyDate;

}