package com.sinoyd.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 解析流方案配置实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "StreamConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class StreamConfig implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public StreamConfig() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column
    private String id = UUIDHelper.NewID();

    /**
     * 方案名
     */
    @Column
    @ApiModelProperty("方案名")
    private String planName;

    /**
     * 流类型(1:文本)
     */
    @Column
    @ApiModelProperty("流类型(1:文本)")
    private String steamType;

    /**
     * 编码格式 默认为空 初始化 UTF8，ASIIC，GB2312
     */
    @Column
    @ApiModelProperty("编码格式")
    private String enCode;

    /**
     * 文本行分割符
     */
    @Column
    @ApiModelProperty("文本行分割符")
    private String textSplit;

    /**
     * 是否批量流
     */
    @ApiModelProperty("是否批量流")
    private Boolean batchStream;

    /**
     * 包头过滤
     */
    @Column
    @ApiModelProperty("包头过滤")
    private String headFilter;

    /**
     * 包尾过滤
     */
    @Column
    @ApiModelProperty("包尾过滤")
    private String tailFilter;

    /**
     * 方案附件
     */
    @Column
    @ApiModelProperty("方案附件")
    private String planFile;

    /**
     * 组织机构Id
     */
    @Column
    @ApiModelProperty("组织机构Id")
    private String orgId;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建日期
     */
    @CreatedDate
    @ApiModelProperty("创建日期")
    private Date createDate;

    /**
     * 修改人
     */
    @Column
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改日期
     */
    @LastModifiedDate
    @ApiModelProperty("修改日期")
    private Date modifyDate;

}