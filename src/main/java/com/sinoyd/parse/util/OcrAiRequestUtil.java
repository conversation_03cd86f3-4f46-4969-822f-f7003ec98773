package com.sinoyd.parse.util;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

@Component
public class OcrAiRequestUtil {

    @Value("${ai-parse.url:http://localhost:8866/predict/ocr_system}")
    private String ocrUrl;

    @Value("${ai-parse.default-prompt:角色：你是位资深的光学数据识别专家,任务：你需要识别图片内容并将内容按照指定的json格式输出,要求,1.禁止将中文转成英文,禁止将单位转成中文,2.数据中的*需要去掉,3.数据不需要单位,4.采样日期和采样时间不需要分开。指定的json格式示例如下{参数名称1:参数值1,参数名称2:参数值2},去掉不必要的换行符,生成的json文本要能支持直接转换为java中的JSONObject,回答不需要其余说明,只需要输出json文本。}")
    private String ocrPrompt;

    /**
     * AI解析请求
     *
     * @param url      AI解析请求地址
     * @param prompt   AI解析提示词
     * @param filePath AI解析图片文件路径
     * @return AI解析结果
     */
    public String doRequest(String url, String prompt, String filePath) {
//        String requestUrl = StringUtil.isNotEmpty(ocrUrl) ? ocrUrl : url;
//        File file = new File(filePath);
//        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
//                .addFormDataPart("prompt", StringUtil.isNotEmpty(prompt) ? prompt : ocrPrompt)
//                .addFormDataPart("image_file", file.getName(),
//                        RequestBody.create(MediaType.parse("application/octet-stream"), file))
//                .build();
//        Request request = new Request.Builder()
//                .url(ocrUrl)
//                .post(body)
//                .build();
//        OkHttpClient client = new OkHttpClient.Builder()
//                .connectTimeout(30, TimeUnit.SECONDS)
//                .readTimeout(60, TimeUnit.SECONDS)
//                .build();
//        try {
//            Response response = client.newCall(request).execute();
//            if (!response.isSuccessful()) {
//                throw new RuntimeException("API call failed: " + response.code());
//            }
//            try (ResponseBody responseBody = response.body();
//                 BufferedReader reader = new BufferedReader(new InputStreamReader(responseBody.byteStream(),
//                         StandardCharsets.UTF_8))) {
//                String line;
//                while ((line = reader.readLine()) != null) {
//                    if (!line.trim().isEmpty() && !line.contains("id:")) {
//                        String json = line.replace("data: ", "");
//                        try {
//                            Boolean done = JSONObject.parseObject(json).getBoolean("done");
//                            if (done) {
//                                JSONObject originObject = JSONObject.parseObject(json).getJSONObject("workflow_result");
//                                container.setAiAnswer(originObject.toJSONString());
//                                container.setRecordId(ocrConfigRecord.getId());
//                                //数据转换
//                                parseOriginData(ocrConfigRecord, initDatalist, paramList, container);
//                                //初始数据保存
//                                ocrConfigRecordRepository.save(ocrConfigRecord);
//                                ocrConfigParamDataRepository.save(initDatalist);
//                                //存储容器
//                                container.setDataList(initDatalist);
//                                String endStr = JsonUtil.toJson(container);
//                                // 立即处理每一行数据，不等待所有数据读取完成
//                                onData.accept(endStr);
//                            } else {
//                                // 立即处理每一行数据，不等待所有数据读取完成
//                                onData.accept(json);
//                            }
//                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
//                            throw new BaseException("AI识别异常");
//                        }
//                        // 添加小延迟，确保数据能够及时发送
//                        try {
//                            Thread.sleep(10);
//                        } catch (InterruptedException e) {
//                            Thread.currentThread().interrupt();
//                            break;
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("Stream API call failed", e);
//            throw new RuntimeException("Stream API call failed", e);
//        }
        return null;
    }
}
