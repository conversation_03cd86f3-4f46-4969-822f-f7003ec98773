package com.sinoyd.parse.util;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;

/**
 * 纳秒时钟类，用于生成精确到纳秒的系统当前时间
 *
 * <AUTHOR>
 * @version V1.0.0 2021/08/02
 * @since V100R001
 */
public class NanoClock extends Clock {
    private final Clock clock;

    private final long initialNanos;

    private final Instant initialInstant;

    public NanoClock() {
        this(Clock.systemUTC());
    }

    public NanoClock(final Clock clock) {
        this.clock = clock;
        initialInstant = clock.instant();
        initialNanos = getSystemNanos();
    }

    @Override
    public ZoneId getZone() {
        return clock.getZone();
    }

    @Override
    public Instant instant() {
        return initialInstant.plusNanos(getSystemNanos() - initialNanos);
    }

    @Override
    public Clock withZone(final ZoneId zone) {
        return new NanoClock(clock.withZone(zone));
    }

    private long getSystemNanos() {
        return System.nanoTime();
    }
}