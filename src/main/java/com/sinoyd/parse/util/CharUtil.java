package com.sinoyd.parse.util;

import com.sinoyd.boot.common.util.StringUtil;

/**
 * 字符相关的工具类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/08/02
 * @since V100R001
 */
public class CharUtil {

    /**
     * 判断输入字符串在数据库中所占的字节数，默认字符串只包含中文和英文字符
     *
     * @param  s 输入的字符串
     * @param  chnLen 一个中文字符在数据库中所占的字节数
     * @param  engLen 一个英文字符在数据库中所占的字节数
     * @return int 在数据库中所占的字节数
     */
    public static int calByteLen(String s, int chnLen, int engLen) {
        int res = 0;
        if (StringUtil.isEmpty(s)) {
            return 0;
        }
        char[] chars = s.toCharArray();
        for (char c : chars) {
            res += isChinese(c) ? chnLen : engLen;
        }
        return res;
    }

    /**
     * 判断输入字符是否是中文字符（包含中文标点符号）
     *
     * @param c 输入的字符
     * @return true: 是中文字符 false：不是中字符
     */
    public static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION) {
            return true;
        }
        return false;
    }
}
