package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 流程配置使用方式枚举
 * <AUTHOR>
 * @version V1.0.0 2020/12/23
 * @since V100R001
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumFlowUseType {
    /**
     * 流程配置使用方式枚举
     */
    CONCAT("1","拼接"),
    REPLACE("2","替换");

    /**
     * 枚举值
     */
    private String value;
    /**
     * 流程配置使用方式名称
     */
    private String name;

    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumFlowUseType.values())
                .collect(Collectors.toMap(EnumFlowUseType::getValue, EnumFlowUseType::getName));
    }
}
