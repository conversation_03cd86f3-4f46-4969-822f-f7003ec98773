package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 解析日志状态枚举
 * <AUTHOR>
 * @version V1.0.0 2020/12/23
 * @since V100R001
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumParseLogStatus {
    /**
     * 解析日志状态枚举
     */
    PARSING("1", "解析中"),
    SUCCESS("2", "解析成功"),
    FAIL("3", "解析失败");
    /**
     * 枚举值
     */
    private String value;
    /**
     * 解析日志状态名称
     */
    private String name;

    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumParseLogStatus.values())
                .collect(Collectors.toMap(EnumParseLogStatus::getValue, EnumParseLogStatus::getName));
    }
}
