package com.sinoyd.parse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文档类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/15
 **/
@Getter
@AllArgsConstructor
public enum EnumDocType {

    /**
     * AI仪器解析应用附件
     */
    AI_PARSE_APP_FILE("AI仪器解析应用附件", "Parse_Ai_App_File"),

    /**
     * AI仪器解析流程日志附件
     */
    AI_PARSE_FLOW_DATA_FILE("AI仪器解析流程日志附件", "Parse_Ai_FlowData_File");

    /**
     * 文档类型名称
     */
    private String docTypeName;

    /**
     * 文档类型id
     */
    private String docTypeId;

    /**
     * 根据文档类型id获取枚举
     *
     * @param docTypeId 文档类型id
     * @return EnumDocType
     */
    public static EnumDocType getEnumByDocTypeId(String docTypeId) {
        for (EnumDocType docType : EnumDocType.values()) {
            if (docType.getDocTypeId().equals(docTypeId)) {
                return docType;
            }
        }
        return null;
    }
}
