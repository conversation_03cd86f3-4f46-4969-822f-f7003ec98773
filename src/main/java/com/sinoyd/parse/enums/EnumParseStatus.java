package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 解析状态枚举
 * <AUTHOR>
 * @version V1.0.0 2020/12/23
 * @since V100R001
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumParseStatus {
    /**
     * 解析状态枚举
     */
    SUCCESS("1", "解析成功"),
    FAIL("2", "解析失败");
    /**
     * 枚举值
     */
    private String value;
    /**
     * 解析状态名称
     */
    private String name;

    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumParseStatus.values())
                .collect(Collectors.toMap(EnumParseStatus::getValue, EnumParseStatus::getName));
    }
}
