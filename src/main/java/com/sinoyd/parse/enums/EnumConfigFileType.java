package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文件类型枚举
 * <AUTHOR>
 * @version V1.0.0 2020/12/23
 * @since V100R001
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumConfigFileType {
    /**
     * 文件类型
     */
    TXT("1","txt"),
    XLS("2","xls"),
    XLSX("3","xlsx"),
    DOC("4","doc"),
    DOCX("5","docx"),
    PDF("6","pdf"),
    MDB("7","mdb"),
    CSV("8","csv");
    /**
     * 枚举值
     */
    private String value;
    /**
     * 类型名称
     */
    private String name;

    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumConfigFileType.values())
                .collect(Collectors.toMap(EnumConfigFileType::getValue, EnumConfigFileType::getName));
    }

}
