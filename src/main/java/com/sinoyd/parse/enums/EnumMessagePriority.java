package com.sinoyd.parse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息优先级枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
@Getter
@AllArgsConstructor
public enum EnumMessagePriority {

    /**
     * 低优先级
     */
    LOW(1, "低"),

    /**
     * 普通优先级
     */
    NORMAL(2, "普通"),

    /**
     * 高优先级
     */
    HIGH(3, "高"),

    /**
     * 紧急优先级
     */
    URGENT(4, "紧急");

    /**
     * 优先级级别
     */
    private final int level;

    /**
     * 优先级描述
     */
    private final String description;
}
