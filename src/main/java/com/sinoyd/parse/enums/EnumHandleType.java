package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 处理方式枚举
 * <AUTHOR>
 * @version V1.0.0 2020/12/23
 * @since V100R001
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumHandleType {
    /**
     * 处理方式枚举
     */
    PARSE("1","解析"),
    UPLOAD("2","上传");

    /**
     * 枚举值
     */
    private String value;
    /**
     * 处理方式
     */
    private String name;

    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumHandleType.values())
                .collect(Collectors.toMap(EnumHandleType::getValue, EnumHandleType::getName));
    }
}
