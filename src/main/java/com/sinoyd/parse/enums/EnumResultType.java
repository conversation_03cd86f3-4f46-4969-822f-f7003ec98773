package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 结果映射配置结果类型枚举
 * <AUTHOR>
 * @version V1.0.0 2020/12/23
 * @since V100R001
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumResultType {
    /**
     * 结果映射配置结果类型枚举
     */
    NAME("1","分析项目"),
    PARAM("2","参数");

    /**
     * 枚举值
     */
    private String value;
    /**
     * 结果映射配置结果类型名称
     */
    private String name;

    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumResultType.values())
                .collect(Collectors.toMap(EnumResultType::getValue, EnumResultType::getName));
    }
}
