package com.sinoyd.parse.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 连接状态枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
@Getter
@AllArgsConstructor
public enum EnumConnectionStatus {

    /**
     * 已连接
     */
    CONNECTED("connected", "已连接"),

    /**
     * 已断开
     */
    DISCONNECTED("disconnected", "已断开"),

    /**
     * 异常
     */
    ERROR("error", "异常");

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;
}
