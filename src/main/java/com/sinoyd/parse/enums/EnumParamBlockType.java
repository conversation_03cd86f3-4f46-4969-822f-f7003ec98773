package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 流程参数配置块类型枚举
 * <AUTHOR>
 * @version V1.0.0 2020/12/23
 * @since V100R001
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumParamBlockType {

    /**
     * 流程参数配置块类型枚举
     */
    COLUMN("1","确定列"),
    ROW("2","确定行"),
    UNSURE("3", "均不确定");
    /**
     * 枚举值
     */
    private String value;
    /**
     * 流程参数配置块类型名称
     */
    private String name;

    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumParamBlockType.values())
                .collect(Collectors.toMap(EnumParamBlockType::getValue, EnumParamBlockType::getName));
    }
}
