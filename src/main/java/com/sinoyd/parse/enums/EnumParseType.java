package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 解析类型枚举
 * <AUTHOR>
 * @version V1.0.0 2020/12/23
 * @since V100R001
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumParseType {
    /**
     * 解析类型枚举
     */
    FILE("1", "文件解析"),
    DATA("2", "数据流解析"),
    FILE_TEST("3", "文件解析调试"),
    /**
     * AI解析类型枚举
     */
    IMAGE_RECOGNITION("4", "图像识别"),
    TEXT_EXTRACTION("5", "文本提取");
    /**
     * 枚举值
     */
    private String value;
    /**
     * 解析类型名称
     */
    private String name;

    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumParseType.values())
                .collect(Collectors.toMap(EnumParseType::getValue, EnumParseType::getName));
    }
}
