package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 流程配置解析类型枚举
 * <AUTHOR>
 * @version V1.0.0 2020/12/23
 * @since V100R001
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumFlowParseType {
    /**
     * 流程配置解析类型枚举
     */
    NO("1","样品编号"),
    NAME("2","分析项目名称"),
    PARAM("3","参数"),
    RESULT("4","测得结果"),
    OTHER("5","其他");

    /**
     * 枚举值
     */
    private String value;
    /**
     * 解析类型名称
     */
    private String name;

    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumFlowParseType.values())
                .collect(Collectors.toMap(EnumFlowParseType::getValue, EnumFlowParseType::getName));
    }
}
