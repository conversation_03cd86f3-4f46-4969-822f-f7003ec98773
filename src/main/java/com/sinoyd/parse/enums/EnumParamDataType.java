package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 流程参数配置数据类型枚举
 * <AUTHOR>
 * @version V1.0.0 2020/12/23
 * @since V100R001
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumParamDataType {
    /**
     * 流程参数配置数据类型枚举
     */
    VALUE("1","值类型"),
    GAP_VALUE("2","间隔值类型"),
    BLOCK("3","块类型"),
    GAP_BLOCK("4","间隔块类型");
    /**
     * 枚举值
     */
    private String value;
    /**
     * 流程参数配置数据类型名称
     */
    private String name;

    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumParamDataType.values())
                .collect(Collectors.toMap(EnumParamDataType::getValue, EnumParamDataType::getName));
    }
}
