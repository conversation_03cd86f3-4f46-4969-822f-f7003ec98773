package com.sinoyd.parse.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * WebSocket连接类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
@Getter
@AllArgsConstructor
public enum EnumWebSocketType {

    /**
     * AI仪器解析实时数据
     */
    AI_PARSE_REALTIME("ai-parse-realtime", "AI仪器解析实时数据");

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据代码获取WebSocket类型
     *
     * @param code 类型代码
     * @return WebSocket类型
     * @throws BaseException 当代码不存在时抛出异常
     */
    public static EnumWebSocketType getByCode(String code) {
        for (EnumWebSocketType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new BaseException("不支持的WebSocket类型: " + code);
    }

    /**
     * 检查代码是否有效
     *
     * @param code 类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        try {
            getByCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
