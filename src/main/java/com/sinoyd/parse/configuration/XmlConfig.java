package com.sinoyd.parse.configuration;

import com.sinoyd.parse.vo.FilePathConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Scope;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.InputStream;

/**
 * 通用XML映射配置类
 *
 * <AUTHOR>
 * @version 5.2.0
 * @since 2023/05/12
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class XmlConfig {

    @Bean
    public FilePathConfigVO getFilePathConfigVO() {
        InputStream fileInputStream = null;
        try {
            JAXBContext ctx = JAXBContext.newInstance(FilePathConfigVO.class);
            Unmarshaller unmarshaller = ctx.createUnmarshaller();
            ClassPathResource classPathResource = new ClassPathResource("filePathConfig.xml");
            fileInputStream = classPathResource.getInputStream();
            return (FilePathConfigVO) unmarshaller.unmarshal(fileInputStream);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            closeResource(fileInputStream);
        }
    }

    /**
     * 关闭资源
     *
     * @param is 输入流
     */
    private void closeResource(InputStream is) {
        if (is != null) {
            try {
                is.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }
}