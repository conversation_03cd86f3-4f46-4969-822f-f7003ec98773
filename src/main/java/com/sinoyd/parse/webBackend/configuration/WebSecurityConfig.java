package com.sinoyd.parse.webBackend.configuration;

import com.sinoyd.boot.auth.server.config.JwtUserConfig;
import com.sinoyd.frame.filter.JWTUserAuthFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * SpringSecurity的配置
 * 通过SpringSecurity的配置，将JWTLoginFilter，JWTAuthenticationFilter组合在一起
 * <AUTHOR> on 2017/9/13.
 */
@Configuration
@EnableWebSecurity//(debug = true)
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfig extends  WebSecurityConfigurerAdapter {

    @Autowired
    private JwtUserConfig jwtUserConfig;

    // 设置 HTTP 验证规则
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable().authorizeRequests()
                .antMatchers("/*", "/gis/**", "/html/**", "/static/**", "/webjars/**", "/v2/api-docs", "/logfile").permitAll()//允许get根目录直属资源，html、css和js下所有资源（含子目录）
                .antMatchers(HttpMethod.POST, "/api/sys/users/findpwd", "/api/auth/jwt/token","/api/sys/users/register/sms", "/api/sys/users/register").permitAll() // 所有 /users/signup 的POST请求 都放行
                .antMatchers(HttpMethod.POST,  "/api/sys/users/findpwd/sms", "/api/sys/orgs", "/api/auth/jwt/choose", "/api/auth/kaptcha/refresh").permitAll() // 所有 /users/signup 的POST请求 都放行
                .antMatchers(HttpMethod.GET, "/api/entity/entityMapping","/api/auth/jwt/refresh").permitAll()
                .anyRequest().authenticated()  // 所有请求需要身份认证
                .and().headers().frameOptions().disable()// 禁用x-frame 、
                .and()
                .addFilter(new JWTUserAuthFilter(authenticationManager(), jwtUserConfig));
    }
}