package com.sinoyd.parse.webBackend.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

/**
 * 仪器解析frame数据源配置(用于访问frame数据源中的t_sys_user,t_auth_user表)
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/11/22
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "instrumentParseFrameEntityManagerFactory",
        transactionManagerRef = "instrumentParseFrameTransactionManager",
        basePackages = {
                "com.sinoyd.parse.repository.frame"
        }
)
public class InstrumentParseFrameConfig {

    private DataSource instrumentParseFrameDataSourceConfig;

    private JpaProperties jpaProperties;

    @Bean(name = "instrumentParseFrameEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean baseEntityManagerFactory(
            EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(instrumentParseFrameDataSourceConfig)
                .packages(
                        "com.sinoyd.parse.dto.frame"
                )
                //后续如果要引用pro的那basePackages要继续加pro的底层数据包，如果引用到视图也要加进来
                .properties(jpaProperties.getHibernateProperties(instrumentParseFrameDataSourceConfig))
                .persistenceUnit("parse")
                .build();
    }

    @Bean(name = "instrumentParseFrameTransactionManager")
    public PlatformTransactionManager baseTransactionManager(
            @Qualifier("instrumentParseFrameEntityManagerFactory") EntityManagerFactory primaryEntityManagerFactory) {
        return new JpaTransactionManager(primaryEntityManagerFactory);
    }

    @Bean(name = "instrumentParseFrameTransactionManager")
    PlatformTransactionManager transactionManagerSecondary(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(baseEntityManagerFactory(builder).getObject());
    }

    @Autowired
    @Qualifier("instrumentParseFrameDataSource")
    public void setInstrumentParseDataSourceConfig(DataSource instrumentParseDataSourceConfig) {
        this.instrumentParseFrameDataSourceConfig = instrumentParseDataSourceConfig;
    }

    @Autowired
    public void setJpaProperties(JpaProperties jpaProperties) {
        this.jpaProperties = jpaProperties;
    }
}