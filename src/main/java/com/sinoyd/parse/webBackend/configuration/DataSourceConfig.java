package com.sinoyd.parse.webBackend.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

/**
 * 多数据源配置（与frame数据源可分开处理）
 * <AUTHOR>
 * @version V1.0.0 2019/07/19
 * @since V100R001
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "primaryEntityManagerFactory",
        transactionManagerRef = "primaryTransactionManager",
        basePackages = {
                "com.sinoyd.parse.repository",
        }
        //后续如果要引用pro的那basePackages要继续加pro的底层数据包
)
public class DataSourceConfig {

    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "primaryDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.primary")
    public DataSource parseDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Primary
    @Bean(name = "entityManagerPrimary")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return baseEntityManagerFactory(builder, parseDataSource()).getObject().createEntityManager();
    }

    @Bean(name = "primaryEntityManagerFactory")
    @Primary
    public LocalContainerEntityManagerFactoryBean baseEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("primaryDataSource") DataSource parseDataSource) {
        return builder
                .dataSource(parseDataSource)
                .packages("com.sinoyd.parse.dto"
                )
                //后续如果要引用pro的那basePackages要继续加pro的底层数据包，如果引用到视图也要加进来
                .properties(jpaProperties.getHibernateProperties(parseDataSource))
                .persistenceUnit("parse")
                .build();
    }

    @Bean(name = "primaryTransactionManager")
    public PlatformTransactionManager baseTransactionManager(
            @Qualifier("primaryEntityManagerFactory") EntityManagerFactory primaryEntityManagerFactory) {
        return new JpaTransactionManager(primaryEntityManagerFactory);
    }

    @Bean(name = "instrumentParseFrameDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.frame")
    public DataSource instrumentParseFrameDataSource() {
        return DataSourceBuilder.create().build();
    }
}