package com.sinoyd.parse.webBackend.configuration;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.annotation.Resource;

/**
 * <p>配置默认的事务管理器</p>
 *
 * <AUTHOR>
 * @version V1.0.0 2020/06/17
 * @since V100R001
 */
@Configuration
@AutoConfigureAfter(DataSourceConfig.class)
public class DefaultTxnManagerConfig implements TransactionManagementConfigurer{

    @Resource(name = "primaryTransactionManager")
    private PlatformTransactionManager defaultTxnManager;

    @Override
    public PlatformTransactionManager annotationDrivenTransactionManager() {
        return defaultTxnManager;
    }
}
