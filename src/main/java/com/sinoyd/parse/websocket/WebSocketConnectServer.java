package com.sinoyd.parse.websocket;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.parse.enums.EnumMessageType;
import com.sinoyd.parse.enums.EnumWebSocketType;
import com.sinoyd.parse.service.WebSocketManagerService;
import com.sinoyd.parse.vo.WSConnectionVO;
import com.sinoyd.parse.vo.WSMessageVO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * WebSocket工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
@Slf4j
public abstract class WebSocketConnectServer {

    /**
     * 获取WebSocket管理器服务
     *
     * @return WebSocket管理器服务
     */
    protected static WebSocketManagerService getWebSocketManagerService() {
        return UniversalWebSocketServer.getWebSocketManagerService();
    }

    /**
     * 关闭WebSocket连接
     *
     * @param sessionId 会话ID
     * @return 是否成功关闭
     */
    public static int closeConnection(String sessionId) {
        WebSocketManagerService service = getWebSocketManagerService();
        if (service != null) {
            return service.removeConnection(sessionId) ? 1 : 0;
        }
        return 0;
    }

    public static WSConnectionVO getConnection(String sessionId) {
        WebSocketManagerService service = getWebSocketManagerService();
        if (service != null) {
            return service.getConnection(sessionId);
        }
        return null;
    }

    /**
     * 获取WebSocket连接统计信息
     *
     * @return 连接统计信息
     */
    public static Map<EnumWebSocketType, Integer> getConnectionStatistics() {
        WebSocketManagerService service = getWebSocketManagerService();
        if (service != null) {
            return service.getConnectionStatistics();
        }
        return null;
    }

    /**
     * 获取指定用户的连接数量
     *
     * @param userId 用户ID
     * @return 连接数量
     */
    public static int getUserConnectionCount(String userId) {
        if (StringUtil.isEmpty(userId)) {
            return 0;
        }

        WebSocketManagerService service = getWebSocketManagerService();
        if (service != null) {
            return service.getUserConnectionCount(userId);
        }
        return 0;
    }

    /**
     * 检查用户是否在线
     *
     * @param userId 用户ID
     * @return 是否在线
     */
    public static boolean isUserOnline(String userId) {
        return getUserConnectionCount(userId) > 0;
    }

    /**
     * 清理无效连接
     *
     * @return 清理的连接数量
     */
    public static int cleanInvalidConnections() {
        WebSocketManagerService service = getWebSocketManagerService();
        if (service != null) {
            return service.cleanInvalidConnections();
        }
        return 0;
    }
}
