package com.sinoyd.parse.websocket;

import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.parse.enums.EnumWebSocketType;
import com.sinoyd.parse.service.WebSocketManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.security.Principal;

/**
 * 通用WebSocket服务器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
@ServerEndpoint(value = "/api/parse/websockets/{type}")
@Component
@Slf4j
public class UniversalWebSocketServer {
    
    private static WebSocketManagerService webSocketManagerService;
    
    /**
     * 注入WebSocket管理器服务
     * 由于WebSocket端点是由容器管理的，不能直接使用@Autowired，需要通过静态方法注入
     *
     * @param webSocketManagerService WebSocket管理器服务
     */
    @Autowired
    public void setWebSocketManagerService(WebSocketManagerService webSocketManagerService) {
        UniversalWebSocketServer.webSocketManagerService = webSocketManagerService;
    }
    
    /**
     * 连接建立成功回调方法
     *
     * @param session  WebSocket会话
     * @param typeCode 连接类型代码
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("type") String typeCode) {
        try {
            // 验证连接类型
            if (!EnumWebSocketType.isValidCode(typeCode)) {
                log.warn("不支持的WebSocket类型: {}, 会话ID: {}", typeCode, session.getId());
                session.close(new CloseReason(CloseReason.CloseCodes.NO_EXTENSION, "不支持的WebSocket类型"));
                return;
            }
            
            EnumWebSocketType type = EnumWebSocketType.getByCode(typeCode);
            String userId = getUserId(session);
            
            // 添加连接到管理器
            webSocketManagerService.addConnection(session, type, userId);
            
            // 发送连接成功消息
            String welcomeMessage = String.format("WebSocket连接成功 - 类型: %s, 会话ID: %s", 
                    type.getDescription(), session.getId());
            session.getBasicRemote().sendText(welcomeMessage);
            
            log.info("WebSocket连接建立成功 - 类型: {}, 会话ID: {}, 用户ID: {}", 
                    type.getDescription(), session.getId(), userId);
            
        } catch (Exception e) {
            log.error("WebSocket连接建立失败 - 会话ID: {}, 类型: {}", session.getId(), typeCode, e);
            try {
                session.close(new CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "连接建立失败"));
            } catch (IOException ex) {
                log.error("关闭WebSocket连接失败 - 会话ID: {}", session.getId(), ex);
            }
        }
    }
    
    /**
     * 连接关闭回调方法
     *
     * @param session WebSocket会话
     */
    @OnClose
    public void onClose(Session session) {
        try {
            webSocketManagerService.removeConnection(session.getId());
            log.info("WebSocket连接关闭 - 会话ID: {}", session.getId());
        } catch (Exception e) {
            log.error("处理WebSocket连接关闭失败 - 会话ID: {}", session.getId(), e);
        }
    }
    
    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送的消息
     * @param session WebSocket会话
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            // 更新最后活跃时间
            webSocketManagerService.updateLastActiveTime(session.getId());
            
            log.debug("收到WebSocket消息 - 会话ID: {}, 消息: {}", session.getId(), message);
            
            // 处理心跳消息
            if ("ping".equals(message)) {
                session.getBasicRemote().sendText("pong");
                return;
            }
            
            // 处理其他业务消息
            handleBusinessMessage(session, message);
            
        } catch (Exception e) {
            log.error("处理WebSocket消息失败 - 会话ID: {}, 消息: {}", session.getId(), message, e);
        }
    }
    
    /**
     * WebSocket发生错误时调用的方法
     *
     * @param session WebSocket会话
     * @param error   错误对象
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket发生错误 - 会话ID: {}", session.getId(), error);
        
        try {
            // 发送错误消息给客户端
            if (session.isOpen()) {
                session.getBasicRemote().sendText("WebSocket连接发生错误，请重新连接");
            }
        } catch (IOException e) {
            log.error("发送错误消息失败 - 会话ID: {}", session.getId(), e);
        }
        
        // 移除连接
        webSocketManagerService.removeConnection(session.getId());
    }
    
    /**
     * 从会话中获取用户ID
     *
     * @param session WebSocket会话
     * @return 用户ID
     */
    private String getUserId(Session session) {
        String userId = "";
        try {
            if (session.getUserPrincipal() != null) {
                Principal principal = session.getUserPrincipal();
                if (principal instanceof UsernamePasswordAuthenticationToken) {
                    UsernamePasswordAuthenticationToken authenticationToken = (UsernamePasswordAuthenticationToken) principal;
                    Object principalObj = authenticationToken.getPrincipal();
                    if (principalObj instanceof CurrentPrincipalUser) {
                        CurrentPrincipalUser currentPrincipalUser = (CurrentPrincipalUser) principalObj;
                        userId = currentPrincipalUser.getUserId();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取用户ID失败 - 会话ID: {}", session.getId(), e);
        }
        return userId;
    }
    
    /**
     * 处理业务消息
     *
     * @param session WebSocket会话
     * @param message 消息内容
     */
    private void handleBusinessMessage(Session session, String message) {
        try {
            // 这里可以根据具体业务需求进行消息处理
            // 例如：解析JSON消息，调用相应的业务服务等
            
            // 简单的回显处理
            String response = String.format("收到消息: %s", message);
            session.getBasicRemote().sendText(response);
            
        } catch (IOException e) {
            log.error("处理业务消息失败 - 会话ID: {}, 消息: {}", session.getId(), message, e);
        }
    }

    /**
     * 获取WebSocket管理器服务
     * 提供给其他类使用
     *
     * @return WebSocket管理器服务
     */
    public static WebSocketManagerService getWebSocketManagerService() {
        return webSocketManagerService;
    }
}
