package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.DatasCriteria;
import com.sinoyd.parse.dto.DtoDatas;
import com.sinoyd.parse.service.DatasService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * Datas服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@Api(tags = "示例: Datas服务")
@RestController
@RequestMapping("api/parse/datas")
public class DatasController extends BaseJpaController<DtoDatas, String, DatasService> {


    /**
     * 分页动态条件查询Datas
     *
     * @param datasCriteria 条件参数
     * @return RestResponse<List < Datas>>
     */
    @ApiOperation(value = "分页动态条件查询Datas", notes = "分页动态条件查询Datas")
    @GetMapping
    public RestResponse<List<DtoDatas>> findByPage(DatasCriteria datasCriteria) {
        PageBean<DtoDatas> pageBean = super.getPageBean();
        RestResponse<List<DtoDatas>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, datasCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询Datas
     *
     * @param id 主键id
     * @return RestResponse<DtoDatas>
     */
    @ApiOperation(value = "按主键查询Datas", notes = "按主键查询Datas")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoDatas> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoDatas> restResponse = new RestResponse<>();
        DtoDatas datas = service.findOne(id);
        restResponse.setData(datas);
        restResponse.setRestStatus(StringUtil.isNull(datas) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增Datas
     *
     * @param datas 实体列表
     * @return RestResponse<DtoDatas>
     */
    @ApiOperation(value = "新增Datas", notes = "新增Datas")
    @PostMapping
    public RestResponse<DtoDatas> create(@RequestBody DtoDatas datas) {
        RestResponse<DtoDatas> restResponse = new RestResponse<>();
        restResponse.setData(service.save(datas));
        return restResponse;
    }

    /**
     * 新增Datas
     *
     * @param datas 实体列表
     * @return RestResponse<DtoDatas>
     */
    @ApiOperation(value = "修改Datas", notes = "修改Datas")
    @PutMapping
    public RestResponse<DtoDatas> update(@RequestBody DtoDatas datas) {
        RestResponse<DtoDatas> restResponse = new RestResponse<>();
        restResponse.setData(service.update(datas));
        return restResponse;
    }

    /**
     * "根据id批量删除Datas
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Datas", notes = "根据id批量删除Datas")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 根据id批量查询Datas
     *
     * @param ids id列表
     * @return 响应结果
     */
    @ApiOperation(value = "根据id批量查询Datas", notes = "根据id批量查询Datas")
    @PostMapping("/ids")
    public RestResponse<List<DtoDatas>> findByIds(@RequestBody List<String> ids) {
        RestResponse<List<DtoDatas>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAll(ids));
        return restResponse;
    }

    @ApiOperation(value = "根据样品编号批量查询Datas", notes = "根据样品编号批量查询Datas")
    @PostMapping("/sampleCodes")
    public RestResponse<List<DtoDatas>> findBySampleCodes(@RequestBody List<String> sampleCodes) {
        RestResponse<List<DtoDatas>> restResponse = new RestResponse<>();
        restResponse.setData(service.findBySampleCodes(sampleCodes));
        return restResponse;
    }
}