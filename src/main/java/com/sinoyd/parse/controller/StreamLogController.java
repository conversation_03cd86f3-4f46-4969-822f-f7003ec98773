package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.StreamLogCriteria;
import com.sinoyd.parse.dto.DtoOrgStream;
import com.sinoyd.parse.dto.DtoStreamDataErrLog;
import com.sinoyd.parse.dto.DtoStreamErrLog;
import com.sinoyd.parse.dto.DtoStreamLog;
import com.sinoyd.parse.service.StreamLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 解析流日志服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Validated
@Api(tags = "解析流日志服务")
@RestController
@RequestMapping("api/parse/streamLog")
public class StreamLogController extends BaseJpaController<DtoStreamLog, String, StreamLogService> {

    /**
     * 分页动态条件查询解析流日志
     *
     * @param streamLogCriteria 条件参数
     * @return RestResponse<List<StreamLog>>
     */
    @ApiOperation(value = "分页动态条件查询解析流日志信息", notes = "分页动态条件查询解析流日志信息")
    @GetMapping
    public RestResponse<List<DtoStreamLog>> findByPage(StreamLogCriteria streamLogCriteria) {
        PageBean<DtoStreamLog> pageBean = super.getPageBean();
        RestResponse<List<DtoStreamLog>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, streamLogCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按解析流日志主键id查询源数据流信息
     *
     * @param id 主键id
     * @return RestResponse<DtoStreamLog>
     */
    @ApiOperation(value = "按解析流日志主键id查询源数据流信息", notes = "按解析流日志主键id查询源数据流信息")
    @GetMapping(path = "/{id}/orgStream")
    public RestResponse<DtoOrgStream> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoOrgStream> restResponse = new RestResponse<>();
        DtoOrgStream orgStream = service.findSourceStream(id);
        restResponse.setData(orgStream);
        restResponse.setRestStatus(StringUtil.isNull(orgStream) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 按解析流日志主键id查询解析数据信息列表及错误日志信息列表
     * @param id 解析流日志主键id
     * @return RestResponse<DtoStreamDataErrLog>>
     */
    @ApiOperation(value = "按解析流日志主键id查询解析数据信息列表及错误日志信息列表", notes = "按解析流日志主键id查询解析数据信息列表及错误日志信息列表")
    @GetMapping(path = "/{id}/streamDataErrLog")
    public RestResponse<DtoStreamDataErrLog> findByPlanId(@PathVariable(name = "id")String id, String paramName) {
        RestResponse<DtoStreamDataErrLog> restResponse = new RestResponse<>();
        DtoStreamDataErrLog dtoStreamDataErrLog = service.findStreamDataErrLog(id, paramName);
        restResponse.setData(dtoStreamDataErrLog);
        restResponse.setRestStatus(StringUtil.isEmpty(dtoStreamDataErrLog.getStreamData()) && StringUtil.isEmpty(dtoStreamDataErrLog.getStreamErrLogs())
                ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 按解析流错误日志主键id查询错误日志详细信息
     * @param id 解析流错误日志主键id
     * @return RestResponse<DtoStreamErrLog>>
     */
    @ApiOperation(value = "按解析流错误日志主键id查询错误日志详细信息", notes = "按解析流错误日志主键id查询错误日志详细信息")
    @GetMapping(path = "/streamErrLog/{id}")
    public RestResponse<DtoStreamErrLog> findErrLog(@PathVariable(name = "id")String id) {
        RestResponse<DtoStreamErrLog> restResponse = new RestResponse<>();
        DtoStreamErrLog dtoStreamErrLog = service.findStreamErrLog(id);
        restResponse.setData(dtoStreamErrLog);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }
    
}