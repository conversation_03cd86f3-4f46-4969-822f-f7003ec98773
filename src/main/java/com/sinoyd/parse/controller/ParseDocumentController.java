package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.ParseDocumentCriteria;
import com.sinoyd.parse.dto.DtoParseDocument;
import com.sinoyd.parse.service.ParseDocumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 仪器解析文件管理服务接口定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Validated
@Api(tags = "仪器解析文件管理服务")
@RestController
@RequestMapping("api/parse/parseDocument")
public class ParseDocumentController extends BaseJpaController<DtoParseDocument, String, ParseDocumentService> {

    /**
     * 分页动态条件查询仪器解析文件
     *
     * @param parseDocumentCriteria 条件参数
     * @return RestResponse<List<DtoParseDocument>>
     */
    @ApiOperation(value = "分页动态条件查询仪器解析文件", notes = "分页动态条件查询仪器解析文件")
    @GetMapping
    public RestResponse<List<DtoParseDocument>> findByPage(ParseDocumentCriteria parseDocumentCriteria) {
        PageBean<DtoParseDocument> pageBean = super.getPageBean();
        RestResponse<List<DtoParseDocument>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, parseDocumentCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 上传文档
     *
     * @param request 请求对象
     * @return RestResponse<List<DtoParseDocument>>
     */
    @ApiOperation(value = "上传文件", notes = "上传文件")
    @PostMapping("/upload")
    public RestResponse<List<DtoParseDocument>> fileUpload(HttpServletRequest request) {
        RestResponse<List<DtoParseDocument>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.uploadFile(request));
        return restResponse;
    }

    /**
     * 文件下载
     *
     * @param documentId 下载的Id
     * @param response   响应流
     * @return 返回数据
     */
    @ApiOperation(value = "下载文档", notes = "下载文档")
    @GetMapping("/download/{documentId}")
    public RestResponse<Void> fileDownload(@PathVariable String documentId, HttpServletResponse response) {
        service.download(documentId, response);
        return new RestResponse<>();
    }

    /**
     * 文件预览
     *
     * @param vo       文件预览传输对象
     * @param response 响应流
     */
    @PostMapping("/preview")
    public void preview(@RequestBody DocumentPreviewVO vo,
                        HttpServletResponse response) {
        service.preview(vo, response);
    }

    /**
     * 提供统一接口获取相应的文件路径
     *
     * @param code 编号
     * @param map  map数据参数
     * @return 返回数据
     */
    @ApiOperation(value = "获取路径", notes = "获取路径")
    @PostMapping("/{code}")
    public RestResponse<String> getDocumentPath(@PathVariable String code, @RequestBody Map<String, Object> map) throws Exception {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.getDocumentPath(code, map));
        return restResp;
    }

    /**
     * 删除附件
     *
     * @param ids 附件id集合
     * @return 删除的条数
     */
    @ApiOperation(value = "删除仪器解析文件", notes = "删除仪器解析文件")
    @DeleteMapping
    public RestResponse<Integer> delete(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }
}
