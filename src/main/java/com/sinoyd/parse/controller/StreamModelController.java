package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.dto.DtoStreamModel;
import com.sinoyd.parse.service.StreamModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 同步采集数据服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/31
 * @since V100R001
 */
@Validated
@Api(tags = "同步采集数据服务")
@RestController
@RequestMapping("api/parse/streamModel")
public class StreamModelController {

    @Autowired
    private StreamModelService streamModelService;

    /**
     * 移动端同步采集数据
     *
     * @param streamModels 需同步的dto对象列表
     * @return RestResponse<List<DtoStreamData>>
     */
    @ApiOperation(value = "同步采集数据服务", notes = "同步采集数据服务")
    @PostMapping
    public RestResponse<String> findByPage(@RequestBody List<DtoStreamModel> streamModels) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = streamModelService.syncData(streamModels);
        restResp.setCount(count);
        return restResp;
    }

}