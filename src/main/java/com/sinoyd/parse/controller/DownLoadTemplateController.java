package com.sinoyd.parse.controller;

import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.parse.service.DownLoadTemplateService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 数据导入模板下载
 *
 * <AUTHOR>
 * @version V1.0.0 2024/12/09
 * @since V100R001
 */
@RestController
@RequestMapping("/api/parse/downloadTemplate")
public class DownLoadTemplateController extends ExceptionHandlerController<DownLoadTemplateService> {
    /**
     * 实验室仪器解析-解析方案配置结果映射导入模板下载
     */
    @GetMapping("/fileResultAlais")
    public void DownLoadFileResultAlais(HttpServletResponse response) {
        service.downLoadTemplateResultAlais(response, "【导入模板】解析方案配置结果映射信息");
    }
}
