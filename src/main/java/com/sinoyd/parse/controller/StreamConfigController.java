package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.StreamConfigCriteria;
import com.sinoyd.parse.dto.DtoDocument;
import com.sinoyd.parse.dto.DtoStreamConfig;
import com.sinoyd.parse.dto.DtoStreamParamConfig;
import com.sinoyd.parse.service.StreamConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 解析流方案配置服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Validated
@Api(tags = "解析流方案配置服务")
@RestController
@RequestMapping("api/parse/streamConfig")
public class StreamConfigController extends BaseJpaController<DtoStreamConfig, String, StreamConfigService> {

    /**
     * 分页动态条件查询解析流方案配置
     *
     * @param streamConfigCriteria 条件参数
     * @return RestResponse<List<StreamConfig>>
     */
    @ApiOperation(value = "分页动态条件查询解析流方案配置信息", notes = "分页动态条件查询解析流方案配置信息")
    @GetMapping
    public RestResponse<List<DtoStreamConfig>> findByPage(StreamConfigCriteria streamConfigCriteria) {
        PageBean<DtoStreamConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoStreamConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, streamConfigCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询解析流方案配置
     *
     * @param id 主键id
     * @return RestResponse<DtoStreamConfig>
     */
    @ApiOperation(value = "按主键查询解析流方案配置", notes = "按主键查询解析流方案配置")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoStreamConfig> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoStreamConfig> restResponse = new RestResponse<>();
        DtoStreamConfig streamConfig = service.findStreamConfig(id);
        restResponse.setData(streamConfig);
        restResponse.setRestStatus(StringUtil.isNull(streamConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 按解析流方案主键id查询解析流方案参数配置信息列表
     * @param id 解析流方案主键id
     * @return RestResponse<List<DtoStreamParamConfig>>
     */
    @ApiOperation(value = "按解析流方案主键id查询解析流方案参数配置信息列表", notes = "按解析流方案主键id查询解析流方案参数配置信息列表")
    @GetMapping(path = "/{id}/streamParamConfig")
    public RestResponse<List<DtoStreamParamConfig>> findByPlanId(@PathVariable(name = "id")String id) {
        RestResponse<List<DtoStreamParamConfig>> restResponse = new RestResponse<>();
        List<DtoStreamParamConfig> streamParamConfigs = service.findParamConfigByPlanId(id);
        restResponse.setData(streamParamConfigs);
        restResponse.setRestStatus(StringUtil.isEmpty(streamParamConfigs) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增解析流方案配置
     *
     * @param streamConfig 实体列表
     * @return RestResponse<DtoStreamConfig>
     */
    @ApiOperation(value = "新增解析流方案配置", notes = "新增解析流方案配置")
    @PostMapping
    public RestResponse<DtoStreamConfig> create(@Validated @RequestBody DtoStreamConfig streamConfig) {
        RestResponse<DtoStreamConfig> restResponse = new RestResponse<>();
        DtoStreamConfig data = service.saveStreamConfig(streamConfig);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(1);
        return restResponse;
    }

    /**
     * 修改解析流方案配置
     *
     * @param streamConfig 实体列表
     * @return RestResponse<DtoStreamConfig>
     */
    @ApiOperation(value = "修改解析流方案配置", notes = "修改解析流方案配置")
    @PutMapping
    public RestResponse<DtoStreamConfig> update(@Validated @RequestBody DtoStreamConfig streamConfig) {
        RestResponse<DtoStreamConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.updateStreamConfig(streamConfig));
        return restResponse;
    }

    /**
     * 根据id列表批量删除解析流方案配置
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除解析流方案配置", notes = "根据id批量删除解析流方案配置")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.deleteByIds(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 上传方案附件
     *
     * @param request   请求对象
     * @param configId 方案id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "上传文档", notes = "上传文档")
    @PostMapping("/upload/{configId}")
    public RestResponse<String> fileUpload(HttpServletRequest request, @PathVariable String configId) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.upload(request, configId));
        return restResponse;
    }

    /**
     * 下载方案附件
     *
     * @param configId  方案id
     * @param response   响应流
     * @return 返回数据
     */
    @ApiOperation(value = "下载文档", notes = "下载文档")
    @GetMapping("/download/{configId}")
    public RestResponse<String> fileDownload(@PathVariable String configId, HttpServletResponse response) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.download(configId, response));
        return restResp;
    }

    /**
     * 查询方案附件列表
     *
     * @param configId  方案id
     * @return 返回数据
     */
    @ApiOperation(value = "查询方案附件列表", notes = "查询方案附件列表")
    @GetMapping("/attach/{configId}")
    public RestResponse<List<DtoDocument>> queryFile(@PathVariable String configId) {
        RestResponse<List<DtoDocument>> restResp = new RestResponse<>();
        restResp.setData(service.getAttachInfo(configId));
        return restResp;
    }

    /**
     * 删除方案附件
     *
     * @param configId  方案id
     * @return 返回数据
     */
    @ApiOperation(value = "删除方案附件", notes = "删除方案附件")
    @DeleteMapping("/attach/{configId}")
    public RestResponse<List<DtoDocument>> deleteFile(@PathVariable String configId) {
        RestResponse<List<DtoDocument>> restResp = new RestResponse<>();
        service.deleteAttachInfo(configId);
        return restResp;
    }
}