package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.parse.dto.DtoFileResultAlais;
import com.sinoyd.parse.service.ImportResultAlaisService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 解析方案结果映射excel导入接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/12/09
 * @since V100R001
 */
@Api(tags = "excel导入")
@RestController
@RequestMapping("/api/parse/import")
public class ImportResultAlaisController extends ExceptionHandlerController<ImportResultAlaisService> {

    /**
     * 环境质量点位导入
     *
     * @param file 导入的excel文件
     * @return RestResponse<T>
     */
    @PostMapping("/resultAlais")
    public RestResponse<List<DtoFileResultAlais>> importResultAlais(MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, request.getParameter("planId"));
        RestResponse<List<DtoFileResultAlais>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.importExcel(file, objectMap, response));
        return restResponse;
    }
}
