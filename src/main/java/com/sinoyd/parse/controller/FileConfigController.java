package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.FileConfigCriteria;
import com.sinoyd.parse.dto.DtoDocument;
import com.sinoyd.parse.dto.DtoFileConfig;
import com.sinoyd.parse.service.FileConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * FileConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@Validated
 @Api(tags = "示例: FileConfig服务")
 @RestController
 @RequestMapping("api/parse/fileConfig")
 public class FileConfigController extends BaseJpaController<DtoFileConfig, String,FileConfigService> {


    /**
     * 分页动态条件查询FileConfig
     * @param fileConfigCriteria 条件参数
     * @return RestResponse<List<FileConfig>>
     */
     @ApiOperation(value = "分页动态条件查询FileConfig", notes = "分页动态条件查询FileConfig")
     @GetMapping
     public RestResponse<List<DtoFileConfig>> findByPage(FileConfigCriteria fileConfigCriteria) {
         PageBean<DtoFileConfig> pageBean = super.getPageBean();
         RestResponse<List<DtoFileConfig>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fileConfigCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FileConfig
     * @param id 主键id
     * @return RestResponse<DtoFileConfig>
     */
     @ApiOperation(value = "按主键查询FileConfig", notes = "按主键查询FileConfig")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFileConfig> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFileConfig> restResponse = new RestResponse<>();
         DtoFileConfig fileConfig = service.findOne(id);
         restResponse.setData(fileConfig);
         restResponse.setRestStatus(StringUtil.isNull(fileConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FileConfig
     * @param fileConfig 实体列表
     * @return RestResponse<DtoFileConfig>
     */
     @ApiOperation(value = "新增FileConfig", notes = "新增FileConfig")
     @PostMapping
     public RestResponse<DtoFileConfig> create(@Validated @RequestBody DtoFileConfig fileConfig) {
         RestResponse<DtoFileConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fileConfig));
         return restResponse;
      }

     /**
     * 新增FileConfig
     * @param fileConfig 实体列表
     * @return RestResponse<DtoFileConfig>
     */
     @ApiOperation(value = "修改FileConfig", notes = "修改FileConfig")
     @PutMapping
     public RestResponse<DtoFileConfig> update(@Validated @RequestBody DtoFileConfig fileConfig) {
         RestResponse<DtoFileConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fileConfig));
         return restResponse;
      }

    /**
     * "根据id批量删除FileConfig
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FileConfig", notes = "根据id批量删除FileConfig")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 上传方案附件
     *
     * @param request
     * @param configId 方案id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "上传文档", notes = "上传文档")
    @PostMapping("/upload/{configId}")
    public RestResponse<String> fileUpload(HttpServletRequest request, @PathVariable String configId) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        String fileName = service.upload(request, configId);
        restResponse.setData(fileName);
        return restResponse;
    }

    /**
     * 下载方案附件
     *
     * @param configId  方案id
     * @param response   响应流
     * @return 返回数据
     */
    @ApiOperation(value = "下载文档", notes = "下载文档")
    @GetMapping("/download/{configId}")
    public RestResponse<String> fileDownload(@PathVariable String configId, HttpServletResponse response) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.download(configId, response));
        return restResp;
    }

    /**
     * 查询方案附件列表
     *
     * @param configId  方案id
     * @return 返回数据
     */
    @ApiOperation(value = "查询方案附件列表", notes = "查询方案附件列表")
    @GetMapping("/attach/{configId}")
    public RestResponse<List<DtoDocument>> queryFile(@PathVariable String configId) {
        RestResponse<List<DtoDocument>> restResp = new RestResponse<>();
        restResp.setData(service.getAttachInfo(configId));
        return restResp;
    }

    /**
     * 删除方案附件
     *
     * @param configId  方案id
     * @return 返回数据
     */
    @ApiOperation(value = "删除方案附件", notes = "删除方案附件")
    @DeleteMapping("/attach/{configId}")
    public RestResponse<List<DtoDocument>> deleteFile(@PathVariable String configId) {
        RestResponse<List<DtoDocument>> restResp = new RestResponse<>();
        service.deleteAttachInfo(configId);
        return restResp;
    }

    /**
     * 实验室解析方案迁移导出
     */
    @PostMapping("/export")
    public void exportFileConfig(@RequestBody List<String> ids, HttpServletResponse response) {
        service.exportConfig(ids, response, "解析方案");
    }

    /**
     * 实验室解析方案迁移导入
     */
    @PostMapping("/import")
    public RestResponse<String> importFileConfig(MultipartFile file, HttpServletResponse response) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.importConfig(file, response);
        restResponse.setMsg("导入成功！");
        return restResponse;
    }


 }