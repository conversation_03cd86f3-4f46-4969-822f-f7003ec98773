package com.sinoyd.parse.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.service.FileParamConfigService;
import com.sinoyd.parse.criteria.FileParamConfigCriteria;
import com.sinoyd.parse.dto.DtoFileParamConfig;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * FileParamConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Validated
 @Api(tags = "示例: FileParamConfig服务")
 @RestController
 @RequestMapping("api/parse/fileParamConfig")
 public class FileParamConfigController extends BaseJpaController<DtoFileParamConfig, String,FileParamConfigService> {


    /**
     * 分页动态条件查询FileParamConfig
     * @param fileParamConfigCriteria 条件参数
     * @return RestResponse<List<FileParamConfig>>
     */
     @ApiOperation(value = "分页动态条件查询FileParamConfig", notes = "分页动态条件查询FileParamConfig")
     @GetMapping
     public RestResponse<List<DtoFileParamConfig>> findByPage(FileParamConfigCriteria fileParamConfigCriteria) {
         PageBean<DtoFileParamConfig> pageBean = super.getPageBean();
         RestResponse<List<DtoFileParamConfig>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fileParamConfigCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FileParamConfig
     * @param id 主键id
     * @return RestResponse<DtoFileParamConfig>
     */
     @ApiOperation(value = "按主键查询FileParamConfig", notes = "按主键查询FileParamConfig")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFileParamConfig> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFileParamConfig> restResponse = new RestResponse<>();
         DtoFileParamConfig fileParamConfig = service.findOne(id);
         restResponse.setData(fileParamConfig);
         restResponse.setRestStatus(StringUtil.isNull(fileParamConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FileParamConfig
     * @param fileParamConfig 实体列表
     * @return RestResponse<DtoFileParamConfig>
     */
     @ApiOperation(value = "新增FileParamConfig", notes = "新增FileParamConfig")
     @PostMapping
     public RestResponse<DtoFileParamConfig> create(@Validated @RequestBody DtoFileParamConfig fileParamConfig) {
         RestResponse<DtoFileParamConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fileParamConfig));
         return restResponse;
      }

     /**
     * 新增FileParamConfig
     * @param fileParamConfig 实体列表
     * @return RestResponse<DtoFileParamConfig>
     */
     @ApiOperation(value = "修改FileParamConfig", notes = "修改FileParamConfig")
     @PutMapping
     public RestResponse<DtoFileParamConfig> update(@Validated @RequestBody DtoFileParamConfig fileParamConfig) {
         RestResponse<DtoFileParamConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fileParamConfig));
         return restResponse;
      }

    /**
     * "根据id批量删除FileParamConfig
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FileParamConfig", notes = "根据id批量删除FileParamConfig")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }