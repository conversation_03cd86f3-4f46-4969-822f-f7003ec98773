package com.sinoyd.parse.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.service.AppFlowDataService;
import com.sinoyd.parse.criteria.AppFlowDataCriteria;
import com.sinoyd.parse.dto.DtoAppFlowData;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * AppFlowData服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Api(tags = "示例: AppFlowData服务")
 @RestController
 @RequestMapping("api/parse/appFlowData")
 public class AppFlowDataController extends BaseJpaController<DtoAppFlowData, String,AppFlowDataService> {


    /**
     * 分页动态条件查询AppFlowData
     * @param appFlowDataCriteria 条件参数
     * @return RestResponse<List<AppFlowData>>
     */
     @ApiOperation(value = "分页动态条件查询AppFlowData", notes = "分页动态条件查询AppFlowData")
     @GetMapping
     public RestResponse<List<DtoAppFlowData>> findByPage(AppFlowDataCriteria appFlowDataCriteria) {
         PageBean<DtoAppFlowData> pageBean = super.getPageBean();
         RestResponse<List<DtoAppFlowData>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, appFlowDataCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询AppFlowData
     * @param id 主键id
     * @return RestResponse<DtoAppFlowData>
     */
     @ApiOperation(value = "按主键查询AppFlowData", notes = "按主键查询AppFlowData")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoAppFlowData> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoAppFlowData> restResponse = new RestResponse<>();
         DtoAppFlowData appFlowData = service.findOne(id);
         restResponse.setData(appFlowData);
         restResponse.setRestStatus(StringUtil.isNull(appFlowData) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增AppFlowData
     * @param appFlowData 实体列表
     * @return RestResponse<DtoAppFlowData>
     */
     @ApiOperation(value = "新增AppFlowData", notes = "新增AppFlowData")
     @PostMapping
     public RestResponse<DtoAppFlowData> create(@RequestBody DtoAppFlowData appFlowData) {
         RestResponse<DtoAppFlowData> restResponse = new RestResponse<>();
         restResponse.setData(service.save(appFlowData));
         return restResponse;
      }

     /**
     * 新增AppFlowData
     * @param appFlowData 实体列表
     * @return RestResponse<DtoAppFlowData>
     */
     @ApiOperation(value = "修改AppFlowData", notes = "修改AppFlowData")
     @PutMapping
     public RestResponse<DtoAppFlowData> update(@RequestBody DtoAppFlowData appFlowData) {
         RestResponse<DtoAppFlowData> restResponse = new RestResponse<>();
         restResponse.setData(service.update(appFlowData));
         return restResponse;
      }

    /**
     * "根据id批量删除AppFlowData
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除AppFlowData", notes = "根据id批量删除AppFlowData")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }