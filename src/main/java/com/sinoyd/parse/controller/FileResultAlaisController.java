package com.sinoyd.parse.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.service.FileResultAlaisService;
import com.sinoyd.parse.criteria.FileResultAlaisCriteria;
import com.sinoyd.parse.dto.DtoFileResultAlais;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * FileResultAlais服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Validated
 @Api(tags = "示例: FileResultAlais服务")
 @RestController
 @RequestMapping("api/parse/fileResultAlais")
 public class FileResultAlaisController extends BaseJpaController<DtoFileResultAlais, String,FileResultAlaisService> {


    /**
     * 分页动态条件查询FileResultAlais
     * @param fileResultAlaisCriteria 条件参数
     * @return RestResponse<List<FileResultAlais>>
     */
     @ApiOperation(value = "分页动态条件查询FileResultAlais", notes = "分页动态条件查询FileResultAlais")
     @GetMapping
     public RestResponse<List<DtoFileResultAlais>> findByPage(FileResultAlaisCriteria fileResultAlaisCriteria) {
         PageBean<DtoFileResultAlais> pageBean = super.getPageBean();
         RestResponse<List<DtoFileResultAlais>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fileResultAlaisCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FileResultAlais
     * @param id 主键id
     * @return RestResponse<DtoFileResultAlais>
     */
     @ApiOperation(value = "按主键查询FileResultAlais", notes = "按主键查询FileResultAlais")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFileResultAlais> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFileResultAlais> restResponse = new RestResponse<>();
         DtoFileResultAlais fileResultAlais = service.findOne(id);
         restResponse.setData(fileResultAlais);
         restResponse.setRestStatus(StringUtil.isNull(fileResultAlais) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FileResultAlais
     * @param fileResultAlais 实体列表
     * @return RestResponse<DtoFileResultAlais>
     */
     @ApiOperation(value = "新增FileResultAlais", notes = "新增FileResultAlais")
     @PostMapping
     public RestResponse<DtoFileResultAlais> create(@Validated @RequestBody DtoFileResultAlais fileResultAlais) {
         RestResponse<DtoFileResultAlais> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fileResultAlais));
         return restResponse;
      }

     /**
     * 新增FileResultAlais
     * @param fileResultAlais 实体列表
     * @return RestResponse<DtoFileResultAlais>
     */
     @ApiOperation(value = "修改FileResultAlais", notes = "修改FileResultAlais")
     @PutMapping
     public RestResponse<DtoFileResultAlais> update(@Validated @RequestBody DtoFileResultAlais fileResultAlais) {
         RestResponse<DtoFileResultAlais> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fileResultAlais));
         return restResponse;
      }

    /**
     * "根据id批量删除FileResultAlais
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FileResultAlais", notes = "根据id批量删除FileResultAlais")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }