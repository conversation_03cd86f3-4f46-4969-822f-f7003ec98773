package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.ParseLogCriteria;
import com.sinoyd.parse.dto.DtoLog;
import com.sinoyd.parse.service.ParseLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * Log服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@Api(tags = "示例: Log服务")
@RestController
@RequestMapping("api/parse/log")
public class ParseLogController extends BaseJpaController<DtoLog, String, ParseLogService> {


    /**
     * 分页动态条件查询Log
     *
     * @param logCriteria 条件参数
     * @return RestResponse<List < Log>>
     */
    @ApiOperation(value = "分页动态条件查询Log", notes = "分页动态条件查询Log")
    @GetMapping
    public RestResponse<List<DtoLog>> findByPage(ParseLogCriteria logCriteria) {
        PageBean<DtoLog> pageBean = super.getPageBean();
        RestResponse<List<DtoLog>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, logCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询Log
     *
     * @param id 主键id
     * @return RestResponse<DtoLog>
     */
    @ApiOperation(value = "按主键查询Log", notes = "按主键查询Log")
    @GetMapping(path = "/{id}")
    public RestResponse<Map<String, Object>> find(@PathVariable(name = "id") String id, String gatherCode, String analyzeItemName, String paramName) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        restResponse.setData(service.getDetails(id, gatherCode, analyzeItemName, paramName));
        return restResponse;
    }

    /**
     * 新增Log
     *
     * @param log 实体列表
     * @return RestResponse<DtoLog>
     */
    @ApiOperation(value = "新增Log", notes = "新增Log")
    @PostMapping
    public RestResponse<DtoLog> create(@RequestBody DtoLog log) {
        RestResponse<DtoLog> restResponse = new RestResponse<>();
        restResponse.setData(service.save(log));
        return restResponse;
    }

    /**
     * 新增Log
     *
     * @param log 实体列表
     * @return RestResponse<DtoLog>
     */
    @ApiOperation(value = "修改Log", notes = "修改Log")
    @PutMapping
    public RestResponse<DtoLog> update(@RequestBody DtoLog log) {
        RestResponse<DtoLog> restResponse = new RestResponse<>();
        restResponse.setData(service.update(log));
        return restResponse;
    }

    /**
     * "根据id批量删除Log
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Log", notes = "根据id批量删除Log")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 下载方案附件
     *
     * @param logId    日志id
     * @param response 响应流
     * @return 返回数据
     */
    @ApiOperation(value = "下载文档", notes = "下载文档")
    @GetMapping("/download/{logId}")
    public RestResponse<String> fileDownload(@PathVariable String logId, HttpServletResponse response) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.download(logId, response));
        return restResp;
    }

    /**
     * 根据id批量查询Log
     *
     * @param ids 主键id列表
     * @return 响应结果
     */
    @ApiOperation(value = "根据id批量查询Log", notes = "根据id批量查询Log")
    @PostMapping("/ids")
    public RestResponse<List<DtoLog>> findByIds(@RequestBody List<String> ids) {
        RestResponse<List<DtoLog>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAll(ids));
        return restResponse;
    }
}