package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.parse.dto.frame.DtoMobileUser;
import com.sinoyd.parse.service.UserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 移动端获取用户信息服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/08/9
 * @since V100R001
 */
@Validated
@Api(tags = "移动端同步用户信息服务")
@RestController
@RequestMapping("api/parse/userInfo")
public class MobileUserInfoController {

    @Autowired
    private UserInfoService userInfoService;

    /**
     * 移动端获取用户信息
     *
     * @return RestResponse<List<DtoMobileUser>>
     */
    @ApiOperation(value = "移动端获取用户信息", notes = "移动端获取用户信息")
    @GetMapping
    public RestResponse<List<DtoMobileUser>> getUserInfo() {
        RestResponse<List<DtoMobileUser>> restResp = new RestResponse<>();
        List<DtoMobileUser> userInfoList = userInfoService.getUserInfo();
        restResp.setRestStatus(StringUtil.isEmpty(userInfoList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(userInfoList);
        restResp.setCount(StringUtil.isEmpty(userInfoList) ? 0 : userInfoList.size());
        return restResp;
    }
}