package com.sinoyd.parse.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.service.FileFlowService;
import com.sinoyd.parse.criteria.FileFlowCriteria;
import com.sinoyd.parse.dto.DtoFileFlow;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * FileFlow服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@Validated
 @Api(tags = "示例: FileFlow服务")
 @RestController
 @RequestMapping("api/parse/fileFlow")
 public class FileFlowController extends BaseJpaController<DtoFileFlow, String,FileFlowService> {


    /**
     * 分页动态条件查询FileFlow
     * @param fileFlowCriteria 条件参数
     * @return RestResponse<List<FileFlow>>
     */
     @ApiOperation(value = "分页动态条件查询FileFlow", notes = "分页动态条件查询FileFlow")
     @GetMapping
     public RestResponse<List<DtoFileFlow>> findByPage(FileFlowCriteria fileFlowCriteria) {
         PageBean<DtoFileFlow> pageBean = super.getPageBean();
         RestResponse<List<DtoFileFlow>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fileFlowCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FileFlow
     * @param id 主键id
     * @return RestResponse<DtoFileFlow>
     */
     @ApiOperation(value = "按主键查询FileFlow", notes = "按主键查询FileFlow")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFileFlow> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFileFlow> restResponse = new RestResponse<>();
         DtoFileFlow fileFlow = service.findOne(id);
         restResponse.setData(fileFlow);
         restResponse.setRestStatus(StringUtil.isNull(fileFlow) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FileFlow
     * @param fileFlow 实体列表
     * @return RestResponse<DtoFileFlow>
     */
     @ApiOperation(value = "新增FileFlow", notes = "新增FileFlow")
     @PostMapping
     public RestResponse<DtoFileFlow> create(@Validated @RequestBody DtoFileFlow fileFlow) {
         RestResponse<DtoFileFlow> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fileFlow));
         return restResponse;
      }

     /**
     * 新增FileFlow
     * @param fileFlow 实体列表
     * @return RestResponse<DtoFileFlow>
     */
     @ApiOperation(value = "修改FileFlow", notes = "修改FileFlow")
     @PutMapping
     public RestResponse<DtoFileFlow> update(@Validated @RequestBody DtoFileFlow fileFlow) {
         RestResponse<DtoFileFlow> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fileFlow));
         return restResponse;
      }

    /**
     * "根据id批量删除FileFlow
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FileFlow", notes = "根据id批量删除FileFlow")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }