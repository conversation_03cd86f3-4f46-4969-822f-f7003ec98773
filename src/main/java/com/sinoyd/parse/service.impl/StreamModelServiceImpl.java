package com.sinoyd.parse.service.impl;

import com.google.gson.Gson;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.parse.dto.DtoStreamData;
import com.sinoyd.parse.dto.DtoStreamModel;
import com.sinoyd.parse.service.StreamModelService;
import com.sinoyd.parse.util.NanoClock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 同步采集数据服务操作接口实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/31
 * @since V100R001
 */
@Service
public class StreamModelServiceImpl implements StreamModelService {

    @Autowired
    private RedisTemplate redisTemplate;


    @Override
    public int syncData(List<DtoStreamModel> streamModels) {
        if (StringUtil.isEmpty(streamModels)) {
            return 0;
        }
        List<String> jsonList = new ArrayList<>();
        Gson gson = new Gson();
        Clock clock = new NanoClock();
        //生成精确度为纳秒的系统当前时间
        String instantStr = Instant.now(clock).plusMillis(TimeUnit.HOURS.toMillis(8)).toString();
        instantStr = instantStr.substring(0, instantStr.length() - 3) + "+08:00";
        for (DtoStreamModel dto : streamModels) {
            dto.setPostTime(instantStr);
            dto.setCreator(PrincipalContextUser.getPrincipal().getUserId());
            dto.setOrgId(PrincipalContextUser.getPrincipal().getOrgId());
            jsonList.add(gson.toJson(dto));
        }

        redisTemplate.setValueSerializer(new StringRedisSerializer());
//        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.opsForList().leftPushAll("Sinoyd_StreamToParse", jsonList);
        return streamModels.size();
    }

}