package com.sinoyd.parse.service.impl;


import com.sinoyd.parse.constants.ParseConstants;
import com.sinoyd.parse.dto.customer.DtoImportFileResultAlais;
import com.sinoyd.parse.enums.EnumResultType;
import com.sinoyd.parse.service.DownLoadTemplateService;
import com.sinoyd.parse.util.ImportUtils;
import com.sinoyd.parse.util.PoiExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 导入模板下载实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/12/09
 * @since V100R001
 */
@Service
@Slf4j
public class DownLoadTemplateServiceImpl implements DownLoadTemplateService {

    private ImportUtils importUtils;

    /**
     * 下载环境质量点位导入模板
     *
     * @param response 响应流
     * @param fileName 文件名
     */
    @Override
    public void downLoadTemplateResultAlais(HttpServletResponse response, String fileName) {
        Map<String, String> sheetNames = new HashMap<>();
        //设置导出的Sheet名称
        sheetNames.put(ParseConstants.ImportConstants.FIRST_SHEET_NAME, "解析方案结果映射");
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportFileResultAlais.class, new ArrayList<>());
        importUtils.selectList(workBook, 0, 0,  new String[]{EnumResultType.NAME.getName(), EnumResultType.PARAM.getName()});
        //下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }
}
