package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.parse.dto.DtoFileConfig;
import com.sinoyd.parse.dto.DtoFileFlow;
import com.sinoyd.parse.enums.EnumFlowParseType;
import com.sinoyd.parse.enums.EnumFlowUseType;
import com.sinoyd.parse.repository.AppFlowDataRepository;
import com.sinoyd.parse.repository.ErrorLogRepository;
import com.sinoyd.parse.repository.FileFlowRepository;
import com.sinoyd.parse.repository.FileParamConfigRepository;
import com.sinoyd.parse.service.FileFlowService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * FileFlow操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Service
public class FileFlowServiceImpl extends BaseJpaServiceImpl<DtoFileFlow,String,FileFlowRepository> implements FileFlowService {
    @Autowired
    private FileParamConfigRepository paramConfigRepository;
    @Autowired
    private AppFlowDataRepository flowDataRepository;
    @Autowired
    private ErrorLogRepository errorLogRepository;

    @Override
    public void findByPage(PageBean<DtoFileFlow> pb, BaseCriteria fileFlowCriteria) {
        pb.setEntityName("DtoFileFlow a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, fileFlowCriteria);
        List<DtoFileFlow> flows = pb.getData();
        Map<String, String> parseTypeMap = EnumFlowParseType.getMapData();
        Map<String, String> useTypeMap = EnumFlowUseType.getMapData();
        for (DtoFileFlow flow : flows) {
            String parseType = flow.getParseType();
            String useType = flow.getUseType();
            if (StringUtil.isNotEmpty(parseType)) {
                flow.setParseTypeName(parseTypeMap.getOrDefault(parseType, ""));
            }
            if (StringUtil.isNotEmpty(useType)) {
                flow.setUseTypeName(useTypeMap.getOrDefault(useType, ""));
            }
        }
    }

    @Override
    @Transactional
    public DtoFileFlow save(DtoFileFlow entity) {
        checkExist(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoFileFlow update(DtoFileFlow entity) {
        checkExist(entity);
        if (!entity.getUseLastData()) {
            entity.setUseType(null);
        }
        return repository.save(entity);
    }

    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        if (StringUtil.isEmpty(ids)) {
            return 0;
        }
        List<String> flowIds = ids.parallelStream().map(Object::toString).collect(Collectors.toList());
        paramConfigRepository.deleteByFlowIds(flowIds, new Date());
        flowDataRepository.deleteByFlowIds(flowIds, new Date());
        errorLogRepository.deleteByFlowIds(flowIds, new Date());
        return super.logicDeleteById(ids);
    }

    @Override
    public void deleteByConfigIds(List<String> configIds) {
        List<DtoFileFlow> flows = repository.findByPlanIdIn(configIds);
        if (StringUtil.isNotEmpty(flows)) {
            this.logicDeleteById(flows.parallelStream().map(DtoFileFlow::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 查重方法
     * @param entity 实体
     */
    private void checkExist(DtoFileFlow entity) {
        Integer count = repository.countByFlowNameAndPlanIdAndIdNot(entity.getFlowName(),
                entity.getPlanId(), entity.getId());
        if (StringUtil.isNotNull(count) && count > 0) {
            throw new BaseException("存在同名流程：" + entity.getFlowName());
        }
        count = repository.countByStepNumAndPlanIdAndIdNot(entity.getStepNum(),
                entity.getPlanId(), entity.getId());
        if (StringUtil.isNotNull(count) && count > 0) {
            throw new BaseException("已存在相同序号");
        }
    }
}