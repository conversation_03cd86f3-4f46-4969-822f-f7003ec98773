package com.sinoyd.parse.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.preview.DocumentPreviewFactory;
import com.sinoyd.common.utils.FileUtil;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.configuration.XmlConfig;
import com.sinoyd.parse.dto.DtoParseDocument;
import com.sinoyd.parse.repository.ParseDocumentRepository;
import com.sinoyd.parse.service.ParseDocumentService;
import com.sinoyd.parse.vo.FilePathConfigItemVO;
import com.sinoyd.parse.vo.UploadParamsVO;
import com.sinoyd.parse.vo.UploadRequestHeadParamsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仪器解析文件管理操作接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Service
@Slf4j
public class ParseDocumentServiceImpl extends BaseJpaServiceImpl<DtoParseDocument, String, ParseDocumentRepository> implements ParseDocumentService {

    @Value("${fileProps.filePath}")
    private String filePath;

    @Override
    public void findByPage(PageBean<DtoParseDocument> pb, BaseCriteria parseDocumentCriteria) {
        pb.setEntityName("DtoParseDocument a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, parseDocumentCriteria);
    }

    @Override
    public List<DtoParseDocument> findByObjectId(String objectId) {
        if (StringUtil.isEmpty(objectId)) {
            return null;
        }
        return repository.findByObjectId(objectId);
    }

    @Override
    public List<DtoParseDocument> uploadFile(UploadParamsVO params, Map<String, List<String>> pathMap) {
        //需要保存的附件数据
        return Collections.emptyList();
    }

    @Override
    @Transactional
    public List<DtoParseDocument> uploadFile(HttpServletRequest request) {
        //使用默认上传设置进行上传
        return uploadFile(UploadParamsVO.defaultParams(), request);
    }

    @Override
    @Transactional
    public List<DtoParseDocument> uploadFile(UploadParamsVO params, HttpServletRequest request) {
        UploadRequestHeadParamsVO headParams = new UploadRequestHeadParamsVO(request);
        //上传的文件数据
        List<MultipartFile> files = headParams.getFiles();
        //需要保存的附件数据
        List<DtoParseDocument> docList = new ArrayList<>();
        //根据uploadPath 处理附件（可能需要存储到不同路径下面）
        String newPath = StringUtils.isNotNullAndEmpty(headParams.getUploadPath()) ? "/" + headParams.getUploadPath() : "";
        //创建文件目录
        String checkPath = filePath + newPath;
        File fileStream = new File(checkPath);
        if (!fileStream.exists()) {
            fileStream.mkdirs();
        }
        //判断是否覆盖上传（如果覆盖上传则查询到目标对象下的对应类型的所有附件数据，不删除对应文件，用于留档)）
        if (headParams.getIsCoverFile() != null && headParams.getIsCoverFile()) {
            List<DtoParseDocument> oldDocs = repository.findByObjectIdAndDocTypeId(headParams.getObjectId(), headParams.getDocTypeId());
            if (StringUtil.isNotEmpty(oldDocs)) {
                //删除附件数据
                List<String> deleteDocIds = oldDocs.parallelStream()
                        .map(DtoParseDocument::getId).collect(Collectors.toList());
                logicDeleteById(deleteDocIds);
            }
        }
        //将返回的fileNames存储到document
        for (MultipartFile multipartFile : files) {

            //文件名称
            String fileName = multipartFile.getOriginalFilename();
            //物理文件名（真实存储的文件名称）
            String physicalName = fileName;
            if (params.getIsEnableTimestamp() != null && params.getIsEnableTimestamp()) {
                //时间戳
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                //物理名称（真实文件名称）
                physicalName = dateFormat.format(new Date()) + "_" + fileName;
            }

            String downPath = newPath + "/" + physicalName;
            //后缀名
            String docSuffix = fileName.substring(fileName.lastIndexOf("."));
            //后期考虑是否做附件后缀限制
            DtoParseDocument dtoDocument = new DtoParseDocument(headParams.getObjectId(), newPath, fileName, physicalName, downPath,
                    headParams.getDocTypeId(), headParams.getDocTypeName(), Integer.parseInt(String.valueOf(multipartFile.getSize())), docSuffix);
            //创建文件
            File file = new File(new File(checkPath).getAbsoluteFile() + File.separator + physicalName);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            try {
                multipartFile.transferTo(file);
                docList.add(dtoDocument);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        return super.save(docList);
    }

    @Override
    public void download(String documentId, HttpServletResponse response) {
        DtoParseDocument document = super.findOne(documentId);
        String path = filePath + document.getPath();
        String filename = document.getFilename();
        FileUtil.download(path, filename, response);
    }

    @Override
    public String getDocumentPath(String code, Map<String, Object> map) {

        FilePathConfigItemVO config = getPathConfigByCode(code);
        if (config == null) {
            throw new BaseException("附件路径未配置!");
        }
        //占位符
        String placeholder = config.getPlaceholder();
        //处理相应的数据源
        String path = config.getPath();

        if (StringUtils.isNotNullAndEmpty(config.getClassName())) {

            try {
                Class cls = Class.forName(config.getClassName());
                Set<String> keys = map.keySet();
                Class<?>[] classes = new Class[keys.size()];
                Object[] objects = new Object[keys.size()];
                Integer i = 0;
                //计算方法参数
                for (String key : keys) {
                    Object value = map.get(key);
                    classes[i] = value.getClass();
                    objects[i] = value;
                    i++;
                }
                Method method = cls.getMethod(config.getMethod(), classes);
                Object data = method.invoke(SpringContextAware.getBean(cls), objects);
                if (StringUtils.isNotNullAndEmpty(placeholder)) {
                    //如果返回的直接是字符串，直接替换
                    if (data instanceof String) {
                        path = path.replace("{" + placeholder + "}", String.valueOf(data));
                    } else {
                        //根据占位符得出相应的数据字段名称
                        String[] fieldNames = placeholder.split(",");
                        if (StringUtil.isNotNull(data)) {
                            Map<String, Object> dataMap = JsonIterator.deserialize(JsonStream.serialize(data), Map.class);
                            for (String fieldName : fieldNames) {
                                String value = String.valueOf(dataMap.get(fieldName));
                                if (!StringUtils.isNotNullAndEmpty(value)) {
                                    value = "";//防止是null值
                                }
                                path = path.replace("{" + fieldName + "}", value.trim().replace("/", "-"));
                            }
                        }
                    }
                }
            } catch (ClassNotFoundException | NoSuchMethodException e) {
                log.error(e.getMessage(), e);
                throw new BaseException("附件路径配置错误!");
            } catch (IllegalAccessException | InvocationTargetException e) {
                log.error(e.getMessage(), e);
                throw new BaseException("附件路径获取方法访问异常!");
            }
        }
        return path;
    }

    @Override
    public void preview(DocumentPreviewVO vo, HttpServletResponse response) {
        String sourceFilePath = vo.getSourceFilePath();
        if (!"/".equals(sourceFilePath.substring(0, 1))) {
            sourceFilePath = String.format("/%s", sourceFilePath);
            vo.setSourceFilePath(sourceFilePath);
        }
        String absPath = filePath + sourceFilePath;
        DocumentPreviewFactory.previewAsPDF(filePath, vo, response);
    }

    /**
     * 获取指定编号的配置信息
     *
     * @param code 配置编号
     * @return 返回配置信息
     */
    private FilePathConfigItemVO getPathConfigByCode(String code) {
        XmlConfig xmlConfig = SpringContextAware.getBean(XmlConfig.class);
        Optional<FilePathConfigItemVO> optionalFilePathConfigItemVO = xmlConfig.getFilePathConfigVO()
                .getFilePathConfigs().stream().filter(p -> p.getCode().equals(code)).findFirst();
        if (StringUtil.isNotNull(optionalFilePathConfigItemVO) && optionalFilePathConfigItemVO.isPresent()) {
            return optionalFilePathConfigItemVO.get();
        }
        return null;
    }
}
