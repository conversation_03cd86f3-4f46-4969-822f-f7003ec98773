package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.frame.DtoMobileUser;
import com.sinoyd.parse.dto.frame.DtoUserInfo;
import com.sinoyd.parse.repository.frame.AuthUserInfoRepository;
import com.sinoyd.parse.repository.frame.UserInfoRepository;
import com.sinoyd.parse.service.UserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * 移动端获取用户信息服务操作接口实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/31
 * @since V100R001
 */
@Service
public class MobileUserInfoServiceImpl extends BaseJpaServiceImpl<DtoMobileUser,String,UserInfoRepository> implements UserInfoService {

    @Autowired
    private AuthUserInfoRepository authUserInfoRepository;

    @Override
    public List<DtoMobileUser> getUserInfo() {
        List<DtoMobileUser> resList = new ArrayList<>();
        resList.addAll(repository.findAll());
        List<DtoUserInfo> authUserInfoList = authUserInfoRepository.findAll();
        if (StringUtil.isNotEmpty(authUserInfoList)) {
            for (DtoUserInfo dto : authUserInfoList) {
                DtoMobileUser mobileUser = new DtoMobileUser(dto.getId(), dto.getLoginID(), dto.getPassword(), dto.getDisplayName(), dto.getOrgGuid());
                resList.add(mobileUser);
            }
        }
        return resList;
    }

}