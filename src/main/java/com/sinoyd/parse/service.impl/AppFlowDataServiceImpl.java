package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoAppFlowData;
import com.sinoyd.parse.dto.DtoLog;
import com.sinoyd.parse.enums.EnumParseStatus;
import com.sinoyd.parse.repository.AppFlowDataRepository;
import com.sinoyd.parse.service.AppFlowDataService;
import com.sinoyd.parse.service.ParseLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * AppFlowData操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@Service
public class AppFlowDataServiceImpl extends BaseJpaServiceImpl<DtoAppFlowData, String, AppFlowDataRepository> implements AppFlowDataService {

    private ParseLogService parseLogService;

    @Override
    public void findByPage(PageBean<DtoAppFlowData> pb, BaseCriteria appFlowDataCriteria) {
        pb.setEntityName("DtoAppFlowData a, DtoLog b");
        pb.setSelect("select a,b");
        comRepository.findByPage(pb, appFlowDataCriteria);
        Map<String, String> parseStatusMap = EnumParseStatus.getMapData();
        List list = pb.getData();
        Iterator iterator = list.iterator();
        List<DtoAppFlowData> resultList = new ArrayList<>();
        while (iterator.hasNext()) {
            Object[] objects = (Object[]) iterator.next();
            DtoAppFlowData flowData = (DtoAppFlowData) objects[0];
            DtoLog log = (DtoLog) objects[1];
            flowData.setFileName(log.getFileOrgName());
            String parseStatus = flowData.getParseStatus();
            if (StringUtil.isNotEmpty(parseStatus)) {
                flowData.setParseStatusName(parseStatusMap.getOrDefault(parseStatus, ""));
            }
            resultList.add(flowData);
        }
        pb.setData(resultList);
    }

    @Override
    public DtoAppFlowData findAttachPath(String id) {
        DtoAppFlowData appFlowData = repository.findOne(id);
        if (appFlowData != null) {
            DtoLog log = parseLogService.findOne(appFlowData.getParselogId());
            if (log != null) {
                appFlowData.setAppId(log.getAppId());
            }
        }
        return appFlowData;
    }

    @Autowired
    public void setParseLogService(ParseLogService parseLogService) {
        this.parseLogService = parseLogService;
    }
}