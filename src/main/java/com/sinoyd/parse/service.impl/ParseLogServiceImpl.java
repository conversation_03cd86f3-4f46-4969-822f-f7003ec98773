package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.*;
import com.sinoyd.parse.entity.*;
import com.sinoyd.parse.enums.EnumHandleType;
import com.sinoyd.parse.enums.EnumParseLogStatus;
import com.sinoyd.parse.enums.EnumParseStatus;
import com.sinoyd.parse.enums.EnumParseType;
import com.sinoyd.parse.repository.*;
import com.sinoyd.parse.service.ParseLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import static com.sinoyd.parse.service.impl.FileConfigServiceImpl.downFile;


/**
 * Log操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Service
 @Slf4j
public class ParseLogServiceImpl extends BaseJpaServiceImpl<DtoLog,String,ParseLogRepository> implements ParseLogService {
    @Autowired
    private AppFlowDataRepository flowDataRepository;
    @Autowired
    private ErrorLogRepository errorLogRepository;
    @Autowired
    private DatasRepository datasRepository;
    @Autowired
    private DatasTestRepository datasTestRepository;
    @Autowired
    private FileFlowRepository flowRepository;
    @Autowired
    private FileAppRepository fileAppRepository;

    @Value("${fileProps.filePath}")
    private String filePath;

    @Override
    public void findByPage(PageBean<DtoLog> pb, BaseCriteria logCriteria) {
        pb.setEntityName("DtoLog a, DtoFileApp b");
        pb.setSelect("select a,b");
        comRepository.findByPage(pb, logCriteria);
        List list = pb.getData();
        Iterator iterator = list.iterator();
        List<DtoLog> logs = new ArrayList<>();
        while (iterator.hasNext()) {
            Object[] objects = (Object[]) iterator.next();
            DtoLog log = (DtoLog) objects[0];
            DtoFileApp app = (DtoFileApp) objects[1];
            log.setInstrumentCode(app.getInstrumentCode());
            log.setInstrumentName(app.getInstrumentName());
            logs.add(log);
        }
        List<String> logIds = logs.parallelStream().map(DtoLog::getId).collect(Collectors.toList());
        // 仅查询调试数据时获取应用流程数据
        List<DtoAppFlowData> appFlowDataList = flowDataRepository.findByParselogIdIn(logIds);
        // logId -> 流程数据列表
        Map<String, List<DtoAppFlowData>> flowDataMap = appFlowDataList.parallelStream().collect(Collectors.groupingBy(AppFlowData::getParselogId));
        Map<String, String> parseTypeMap = EnumParseType.getMapData();
        Map<String, String> parseStatusMap = EnumParseLogStatus.getMapData();
        Map<String, String> handleTypeMap = EnumHandleType.getMapData();
        for (DtoLog log : logs) {
            String parseType = log.getParseType();
            String parseStatus = log.getParseStatus();
            String handleType = log.getHandleType();
            if (StringUtil.isNotEmpty(parseType)) {
                log.setParseTypeName(parseTypeMap.getOrDefault(parseType, ""));
            }
            if (StringUtil.isNotEmpty(parseStatus)) {
                log.setParseStatusName(parseStatusMap.getOrDefault(parseStatus, ""));
            }
            if (StringUtil.isNotEmpty(handleType)) {
                log.setHandleTypeName(handleTypeMap.getOrDefault(handleType, ""));
            }
            if (StringUtil.isNotEmpty(flowDataMap)) {
                List<DtoAppFlowData> flowDataList = flowDataMap.getOrDefault(log.getId(),null);
                if (StringUtil.isNotEmpty(flowDataList)) {
                    flowDataList = flowDataList.stream().sorted(Comparator.comparing(AppFlowData::getParseData))
                            .collect(Collectors.toList());
                    log.setFlowDataList(flowDataList);
                }
            }
        }
        pb.setData(logs);
    }

    @Override
    public Map<String, Object> getDetails(String id, String gatherCode, String analyzeItemName, String paramName) {
        DtoLog log = repository.findOne(id);
        String parseType = log.getParseType();
        Map<String, Object> resultMap = new HashMap<>();
        // 数据获取
        List<DtoAppFlowData> appFlowDataList = flowDataRepository.findByParselogId(id)
                .stream().sorted(Comparator.comparing(AppFlowData::getParseTime, Comparator.reverseOrder())).collect(Collectors.toList());
        List<DtoErrorLog> errorLogList = errorLogRepository.findByParselogId(id)
                .stream().sorted(Comparator.comparing(ErrorLog::getErrorTime, Comparator.reverseOrder())).collect(Collectors.toList());
        if (EnumParseType.FILE_TEST.getValue().equals(parseType)) {
            List<DtoDatasTest> datasTestList = datasTestRepository.findByParselogId(id);
            if (StringUtil.isNotEmpty(gatherCode)) {
                datasTestList = datasTestList.stream().filter(p -> StringUtil.isNotEmpty(p.getSampleCode())
                        && p.getSampleCode().toLowerCase().contains(gatherCode.toLowerCase())).collect(Collectors.toList());
            }
            if (StringUtil.isNotEmpty(analyzeItemName)) {
                datasTestList = datasTestList.stream().filter(p -> StringUtil.isNotEmpty(p.getAnalyzeItemName())
                        && p.getAnalyzeItemName().toLowerCase().contains(analyzeItemName.toLowerCase())).collect(Collectors.toList());
            }
            if (StringUtil.isNotEmpty(paramName)) {
                datasTestList = datasTestList.stream().filter(p -> StringUtil.isNotEmpty(p.getParamName())
                        && p.getParamName().toLowerCase().contains(paramName.toLowerCase())).collect(Collectors.toList());
            }
            datasTestList = datasTestList.stream().sorted(Comparator.comparing(DatasTest::getParseDataTime, Comparator.reverseOrder())).collect(Collectors.toList());
            resultMap.put("data", datasTestList);
        } else {
            List<DtoDatas> datasList = datasRepository.findByParselogId(id);
            if (StringUtil.isNotEmpty(gatherCode)) {
                datasList = datasList.stream().filter(p -> StringUtil.isNotEmpty(p.getSampleCode())
                        && p.getSampleCode().toLowerCase().contains(gatherCode.toLowerCase())).collect(Collectors.toList());
            }
            if (StringUtil.isNotEmpty(analyzeItemName)) {
                datasList = datasList.stream().filter(p -> StringUtil.isNotEmpty(p.getAnalyzeItemName())
                        && p.getAnalyzeItemName().toLowerCase().contains(analyzeItemName.toLowerCase())).collect(Collectors.toList());
            }
            if (StringUtil.isNotEmpty(paramName)) {
                datasList = datasList.stream().filter(p -> StringUtil.isNotEmpty(p.getParamName())
                        && p.getParamName().toLowerCase().contains(paramName.toLowerCase())).collect(Collectors.toList());
            }
            datasList = datasList.stream().sorted(Comparator.comparing(Datas::getParseDataTime, Comparator.reverseOrder())).collect(Collectors.toList());
            resultMap.put("data", datasList);
        }
        // 组装枚举
        Map<String, String> parseStatusMap = EnumParseStatus.getMapData();
        for (DtoAppFlowData appFlowData : appFlowDataList) {
            String parseStatus = appFlowData.getParseStatus();
            if (StringUtil.isNotEmpty(parseStatus)) {
                appFlowData.setParseStatusName(parseStatusMap.getOrDefault(parseStatus, ""));
            }
        }
        List<String> flowIds = errorLogList.parallelStream().filter(p -> StringUtil.isNotEmpty(p.getFlowId())).map(ErrorLog::getFlowId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(flowIds)) {
            Map<String, String> flowNameMap = flowRepository.findAll(flowIds).parallelStream()
                    .collect(Collectors.toMap(FileFlow::getId, FileFlow::getFlowName));
            for (DtoErrorLog errorLog : errorLogList) {
                errorLog.setFlowName(flowNameMap.getOrDefault(errorLog.getFlowId(), ""));
            }
        }

        resultMap.put("log", appFlowDataList);
        resultMap.put("error", errorLogList);
        return resultMap;
    }

    @Override
    public String download(String logId, HttpServletResponse response) {
        DtoLog dtoLog = repository.findOne(logId);
        if (StringUtil.isNull(dtoLog)) {
            throw new BaseException("解析日志不存在!");
        }
        DtoFileApp fileApp = fileAppRepository.findOne(dtoLog.getAppId());
        if (StringUtil.isNull(fileApp)) {
            throw new BaseException("解析应用不存在!");
        }
        String str = "2".equals(dtoLog.getParseStatus()) ? "success" : "failure";
        String folderName = fileApp.getFolderName();
        String path = filePath + "/" + str + folderName.substring(folderName.indexOf("/")) + "/" + dtoLog.getFileName();
        log.info("开始下载解析日志文件，path = " + path);
        File file = new File(path);
        if (!file.exists()) {
            log.error("文件不存在：" + path);
            path = path.replace("//", "/");
            log.info("开始下载解析日志文件，path = " + path);
            file = new File(path);
            if (!file.exists()) {
                log.error("文件不存在：" + path);
                throw new BaseException("文件不存在,请确认 " + path);
            }
        }
        String fileName = path.substring(path.lastIndexOf("/") + 1);
        downFile(file, response, fileName);
        return fileName;
    }
}