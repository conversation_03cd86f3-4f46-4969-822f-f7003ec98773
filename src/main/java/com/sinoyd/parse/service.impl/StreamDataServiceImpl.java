package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoMobileParam;
import com.sinoyd.parse.dto.DtoMobileStreamData;
import com.sinoyd.parse.dto.DtoStreamData;
import com.sinoyd.parse.repository.StreamDataRepository;
import com.sinoyd.parse.service.StreamDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.*;


/**
 * 现场解析数据访问操作接口实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Service
public class StreamDataServiceImpl extends BaseJpaServiceImpl<DtoStreamData, String, StreamDataRepository> implements StreamDataService {

    @Autowired
    private CommonRepository commonRepository;

    @Override
    public void findByPage(PageBean<DtoStreamData> pb, BaseCriteria streamDataCriteria) {
        pb.setEntityName("DtoStreamData a, DtoStreamLog b, DtoStreamApp c");
        pb.setSelect("select new com.sinoyd.parse.dto.DtoStreamData(a.serialNumber, a.paramType, a.paramName, a.paramAlias, a.value, a.unit, b.getTime," +
                " a.parseDataTime, c.instrumentName, c.instrumentCode, c.appName) ");
        super.findByPage(pb, streamDataCriteria);
    }

    @Override
    public List<DtoMobileStreamData> findMobileStreamData(BaseCriteria streamDataMobileCriteria) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select new com.sinoyd.parse.dto.DtoStreamData(a.serialNumber, b.getTime, a.instrumentName, ");
        sb.append(" a.instrumentCode, a.paramName, a.paramAlias, a.value, a.unit) ");
        sb.append(" from DtoStreamData a, DtoStreamLog b where 1=1 ");
        sb.append(streamDataMobileCriteria.getCondition());
        List<DtoStreamData> dtoStreamDataList = commonRepository.find(sb.toString(), streamDataMobileCriteria.getValues());

        List<DtoMobileStreamData> resList = new ArrayList<>();
        if (StringUtil.isEmpty(dtoStreamDataList)) {
            return resList;
        }
        //收集每个 serialNumber 对应的 DtoMobileStreamData 对象,放入 resList 中
        DtoStreamData tmpData = dtoStreamDataList.get(0);
        String preSerialNO = tmpData.getSerialNumber();
        String curSerialNO;
        List<DtoMobileParam> dtoMobileParamList = new ArrayList<>();
        dtoMobileParamList.add(new DtoMobileParam(tmpData.getParamName(), tmpData.getParamAlias(), tmpData.getValue(), tmpData.getUnit()));
        DtoMobileStreamData dtoMobileStreamData = new DtoMobileStreamData(preSerialNO, tmpData.getGetTime(), tmpData.getInstrumentName(),
                tmpData.getInstrumentCode(), dtoMobileParamList);
        resList.add(dtoMobileStreamData);
        for (int i = 1; i < dtoStreamDataList.size(); i++) {
            DtoStreamData loopData = dtoStreamDataList.get(i);
            curSerialNO = StringUtil.isNotNull(loopData.getSerialNumber()) ? loopData.getSerialNumber() : "";
            if (curSerialNO.equals(preSerialNO)) {
                resList.get(resList.size() - 1).getParamList().add(new DtoMobileParam(loopData.getParamName(),
                        loopData.getParamAlias(), loopData.getValue(), loopData.getUnit()));
            } else {
                List<DtoMobileParam> loopDtoMobileParamList = new ArrayList<>();
                loopDtoMobileParamList.add(new DtoMobileParam(loopData.getParamName(), loopData.getParamAlias(), loopData.getValue(), loopData.getUnit()));
                DtoMobileStreamData loopDtoMobileStreamData = new DtoMobileStreamData(curSerialNO, loopData.getGetTime(),
                        loopData.getInstrumentName(), loopData.getInstrumentCode(), loopDtoMobileParamList);
                resList.add(loopDtoMobileStreamData);
                preSerialNO = curSerialNO;
            }
        }
        return resList;
    }

}