package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.*;
import com.sinoyd.parse.repository.InstrumentConfigRepository;
import com.sinoyd.parse.repository.StreamAppRepository;
import com.sinoyd.parse.repository.StreamConfigRepository;
import com.sinoyd.parse.service.StreamAppService;
import com.sinoyd.parse.util.CharUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.Collator;
import java.util.*;


/**
 * 解析流应用数据访问操作接口实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Service
public class StreamAppServiceImpl extends BaseJpaServiceImpl<DtoStreamApp, String, StreamAppRepository> implements StreamAppService {


    /*
     * 解析流方案信息数据访问操作接口
     */
    @Autowired
    private StreamConfigRepository streamConfigRepository;

    /*
     * 仪器配置数据访问操作接口
     */
    @Autowired
    private InstrumentConfigRepository instrumentConfigRepository;

    @Autowired
    private CommonRepository commonRepository;

    @Override
    public void findByPage(PageBean<DtoStreamApp> pb, BaseCriteria streamAppCriteria) {
        pb.setEntityName("DtoStreamApp a, DtoStreamConfig b");
        pb.setSelect("select new com.sinoyd.parse.dto.DtoStreamApp(a.id, a.appName, a.instrumentName, a.instrumentCode, b.planName)");
        super.findByPage(pb, streamAppCriteria);
    }

    @Override
    public DtoStreamAppInstrumentConfig findStreamApp(String id) {
        DtoStreamApp streamApp = repository.findOne(id);
        if (StringUtil.isNull(streamApp) || streamApp.getIsDeleted()) {
            throw new BaseException("解析流应用不存在或已删除!");
        }
        String instConfigId = streamApp.getInstrumentConfigId();
        DtoInstrumentConfig instConfig = instrumentConfigRepository.findOne(instConfigId);
        //查询解析流方案信息
        String planId = streamApp.getPlanId();
        DtoStreamConfig streamConfig = streamConfigRepository.findOne(planId);
        return new DtoStreamAppInstrumentConfig(streamApp.getId(),
                streamApp.getAppName(), planId, streamConfig.getPlanName(), streamApp.getInstrumentName(),
                streamApp.getInstrumentCode(), streamApp.getManufacturer(), instConfig.getAnalysisType(), instConfig.getFluidControl(),
                instConfig.getResponsePeriod(), instConfig.getResponse(), instConfig.getBaudRate(), instConfig.getRsCross(),
                instConfig.getUsbBaudRate(), instConfig.getPin());
    }


    @Override
    @Transactional
    public DtoStreamAppInstrumentConfig saveStreamApp(DtoStreamAppInstrumentConfig dtoStrAppInstConfig) {
        if (StringUtil.isNull(dtoStrAppInstConfig)) {
            throw new BaseException("解析流应用信息不能为空!");
        }
        String planId = dtoStrAppInstConfig.getPlanId();
        if (StringUtil.isEmpty(planId)) {
            throw new BaseException("解析流方案id不能为空!");
        }
        DtoStreamConfig dtoStreamConfig = streamConfigRepository.findOne(planId);
        if (StringUtil.isNull(dtoStreamConfig)) {
            throw new BaseException("解析流方案不存在或已删除!");
        }
        String appName = dtoStrAppInstConfig.getAppName();
        //若新增后的新名称长度超出限制则直接抛出异常
        if (appName.length() > 25 && CharUtil.calByteLen(appName, 2, 1) > 50) {
            throw new BaseException("应用名称长度超出限制,不能新增！");
        }
        List<DtoStreamApp> oriDtoStreamAppList = repository.findByAppNameAndIsDeleted(appName, false);
        if (StringUtil.isNotEmpty(oriDtoStreamAppList)) {
            throw new BaseException("存在同名应用!");
        }
        //先新增仪器配置信息
        DtoInstrumentConfig dtoInstrumentConfig = new DtoInstrumentConfig(dtoStreamConfig.getPlanName(), null, null,
                dtoStrAppInstConfig.getAnalysisType(), dtoStrAppInstConfig.getFluidControl(), dtoStrAppInstConfig.getResponse(),
                dtoStrAppInstConfig.getResponsePeriod(), dtoStrAppInstConfig.getCross(), dtoStrAppInstConfig.getBaudRate(),
                dtoStrAppInstConfig.getUsbBaudRate(), dtoStrAppInstConfig.getPin());
        instrumentConfigRepository.save(dtoInstrumentConfig);
        //再新增解析流应用
        DtoStreamApp dtoStreamApp = new DtoStreamApp(dtoStrAppInstConfig.getAppName(), null, dtoStrAppInstConfig.getInstrumentName(),
                dtoStrAppInstConfig.getInstrumentCode(), dtoStrAppInstConfig.getManufacturer(), planId, dtoInstrumentConfig.getId());
        repository.save(dtoStreamApp);
        dtoStrAppInstConfig.setAppId(dtoStreamApp.getId());
        dtoStrAppInstConfig.setPlanName(dtoStreamConfig.getPlanName());
        return dtoStrAppInstConfig;
    }

    @Override
    @Transactional
    public List<DtoStreamApp> copyStreamApp(List<String> ids) {
        if (StringUtil.isEmpty(ids)) {
            throw new BaseException("解析流应用id列表不能为空！");
        }
        List<DtoStreamApp> dtoStreamApps = repository.findByIdInAndIsDeleted(ids, false);
        List<DtoStreamApp> copyStreamApps = new ArrayList<>();
        if (StringUtil.isEmpty(dtoStreamApps)) {
            return copyStreamApps;
        }
        List<DtoInstrumentConfig> copyInstConfigs = new ArrayList<>();
        List<String> instConfigIds = new ArrayList<>();
        //要复制的解析应用的应用名称 加上“[复制]” 后的新名称列表，用于判断这些新名称是否已存在
        List<String> validateAppNames = new ArrayList<>();
        for (DtoStreamApp dtoStreamApp : dtoStreamApps) {
            String validateAppName = dtoStreamApp.getAppName() + "[复制]";
            //若新名称长度超出限制则直接抛出异常
            if (validateAppName.length() > 25 && CharUtil.calByteLen(validateAppName, 2, 1) > 50) {
                throw new BaseException("应用名称 " + validateAppName + " 长度超出限制,不能复制！");
            }
            validateAppNames.add(validateAppName);
        }
        //从数据库中验证加上“[复制]” 后的新名称是否已存在
        List<DtoStreamApp> validateDtoStreamAppList = repository.findByAppNameInAndIsDeleted(validateAppNames, false);
        List<String> existAppNames = new ArrayList<>();
        for (DtoStreamApp dto : validateDtoStreamAppList) {
            existAppNames.add(dto.getAppName());
        }

        //遍历复制后的新名称，若当前遍历的新名称在数据库中已存在，则不进行复制
        for (int i = 0; i < validateAppNames.size(); i++) {
            if (existAppNames.contains(validateAppNames.get(i))) {
                continue;
            }
            DtoStreamApp loopDto = dtoStreamApps.get(i);
            DtoStreamApp copyStreamApp = new DtoStreamApp(validateAppNames.get(i), loopDto.getInstrumentId(), loopDto.getInstrumentName(),
                    loopDto.getInstrumentCode(), loopDto.getManufacturer(), loopDto.getPlanId(), loopDto.getInstrumentConfigId());
            copyStreamApps.add(copyStreamApp);
            instConfigIds.add(loopDto.getInstrumentConfigId());
        }
        //copyStreamApps 为空表示所有新名称都已存在，直接返回
        if (StringUtil.isEmpty(copyStreamApps)) {
            throw new BaseException("存在同名解析流应用！");
        }

        List<DtoInstrumentConfig> dtoInstrumentConfigs = instrumentConfigRepository.findByIdIn(instConfigIds);
        //建立仪器配置 id 和仪器配置dto对象的映射关系
        Map<String, DtoInstrumentConfig> instrumentConfigMap = new HashMap<>();
        for (DtoInstrumentConfig dtoInstrumentConfig : dtoInstrumentConfigs) {
            if (dtoInstrumentConfig.getIsDeleted()) {
                continue;
            }
            instrumentConfigMap.put(dtoInstrumentConfig.getId(), dtoInstrumentConfig);
        }
        //遍历要新增的解析流应用dto对象列表，设置其中每个dto对象的仪器配置信息
        for (DtoStreamApp copyStreamApp : copyStreamApps) {
            DtoInstrumentConfig loopInstConfig = instrumentConfigMap.get(copyStreamApp.getInstrumentConfigId());
            DtoInstrumentConfig copyInstConfig = new DtoInstrumentConfig(loopInstConfig.getName(), loopInstConfig.getBleServerName(),
                    loopInstConfig.getLogOut(), loopInstConfig.getAnalysisType(), loopInstConfig.getFluidControl(),
                    loopInstConfig.getResponse(), loopInstConfig.getResponsePeriod(), loopInstConfig.getRsCross(),
                    loopInstConfig.getBaudRate(), loopInstConfig.getUsbBaudRate(), loopInstConfig.getPin());
            copyStreamApp.setInstrumentConfigId(copyInstConfig.getId());
            copyStreamApp.setPlanName(copyInstConfig.getName());
            copyInstConfigs.add(copyInstConfig);
        }
        //新增仪器配置信息列表
        instrumentConfigRepository.save(copyInstConfigs);
        //新增解析流应用列表
        return repository.save(copyStreamApps);
    }

    @Override
    @Transactional
    public DtoStreamAppInstrumentConfig updateStreamApp(DtoStreamAppInstrumentConfig entity) {
        if (StringUtil.isEmpty(entity.getAppId())) {
            throw new BaseException("解析流应用id不能为空！");
        }
        DtoStreamApp oriDtoStreamApp = repository.findOne(entity.getAppId());
        if (StringUtil.isNull(oriDtoStreamApp) || oriDtoStreamApp.getIsDeleted()) {
            throw new BaseException("解析流应用不存在或已被删除");
        }
        String planId = entity.getPlanId();
        if (StringUtil.isEmpty(planId)) {
            throw new BaseException("解析流方案id不能为空!");
        }
        String appName = entity.getAppName();
        if (StringUtil.isEmpty(appName)) {
            throw new BaseException("解析流应用名称不能为空!");
        }
        //修改了应用名称则需要判断是否存在同名应用
        if (!appName.equals(oriDtoStreamApp.getAppName())) {
            //若新名称长度超出限制则直接抛出异常
            if (appName.length() > 25 && CharUtil.calByteLen(appName, 2, 1) > 50) {
                throw new BaseException("应用名称长度超出限制,不能修改！");
            }
            List<DtoStreamApp> dtoStreamAppList = repository.findByAppNameAndIsDeleted(appName, false);
            if (StringUtil.isNotEmpty(dtoStreamAppList)) {
                throw new BaseException("存在同名应用!");
            }
        }

        //先更新仪器配置
        DtoInstrumentConfig oriInstConfig = instrumentConfigRepository.findOne(oriDtoStreamApp.getInstrumentConfigId());
        DtoInstrumentConfig instrumentConfig = new DtoInstrumentConfig(entity.getPlanName(), oriInstConfig.getBleServerName(),
                oriInstConfig.getLogOut(), entity.getAnalysisType(), entity.getFluidControl(), entity.getResponse(), entity.getResponsePeriod(),
                entity.getCross(), entity.getBaudRate(), entity.getUsbBaudRate(), entity.getPin());
        instrumentConfig.setId(oriInstConfig.getId());
        instrumentConfig.setCreator(oriInstConfig.getCreator());
        instrumentConfig.setCreateDate(oriInstConfig.getCreateDate());
        instrumentConfigRepository.save(instrumentConfig);
        //再更新解析流应用
        DtoStreamApp streamApp = new DtoStreamApp(entity.getAppName(), oriDtoStreamApp.getInstrumentId(), entity.getInstrumentName(),
                entity.getInstrumentCode(), entity.getManufacturer(), entity.getPlanId(), oriDtoStreamApp.getInstrumentConfigId());
        streamApp.setId(entity.getAppId());
        streamApp.setCreator(oriDtoStreamApp.getCreator());
        streamApp.setCreateDate(oriDtoStreamApp.getCreateDate());
        repository.save(streamApp);
        return entity;
    }

    @Override
    @Transactional
    public Integer deleteByIds(List<String> ids) {
        if (StringUtil.isEmpty(ids)) {
            return 0;
        }
        //先删除仪器配置信息
        List<DtoStreamApp> dtoStreamApps = repository.findByIdInAndIsDeleted(ids, false);
        List<String> instConfigIds = new ArrayList<>();
        for (DtoStreamApp dtoStreamApp : dtoStreamApps) {
            if (!instConfigIds.contains(dtoStreamApp.getInstrumentConfigId())) {
                instConfigIds.add(dtoStreamApp.getInstrumentConfigId());
            }
        }
        if (StringUtil.isNotEmpty(instConfigIds)) {
            instrumentConfigRepository.logicDeleteById(instConfigIds, new Date());
        }
        //再删除解析流应用信息
        return super.logicDeleteById(ids);
    }

    @Override
    public List<DtoStreamAppMobileSync> findMobileStreamApp() {

        StringBuilder sb = new StringBuilder();
        sb.append(" select new com.sinoyd.parse.dto.DtoStreamAppMobileSync ");
        sb.append(" (a.id, a.instrumentName, a.instrumentCode, a.manufacturer, b.analysisType, ");
        sb.append(" b.fluidControl, b.response, b.responsePeriod, b.rsCross, b.baudRate, ");
        sb.append(" b.usbBaudRate, b.pin) ");
        sb.append(" from DtoStreamApp a, DtoInstrumentConfig b ");
        sb.append(" where a.instrumentConfigId = b.id ");
        sb.append(" and a.isDeleted = 0 ");
        sb.append(" and b.isDeleted = 0 ");
        sb.append(" order by a.instrumentName");
        List<DtoStreamAppMobileSync> dtoStreamAppMobileSyncList = commonRepository.find(sb.toString());
        return dtoStreamAppMobileSyncList;
    }

}