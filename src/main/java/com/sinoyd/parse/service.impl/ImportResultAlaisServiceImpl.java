package com.sinoyd.parse.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.parse.dto.DtoFileResultAlais;
import com.sinoyd.parse.dto.customer.DtoImportFileResultAlais;
import com.sinoyd.parse.enums.EnumResultType;
import com.sinoyd.parse.repository.FileResultAlaisRepository;
import com.sinoyd.parse.service.ImportResultAlaisService;
import com.sinoyd.parse.util.PoiExcelUtils;
import com.sinoyd.parse.verify.ResultAlaisVerifyHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 解析方案结果映射导入实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/12/09
 * @since V100R001
 */
@Service
@Slf4j
public class ImportResultAlaisServiceImpl implements ImportResultAlaisService {

    private ResultAlaisVerifyHandle resultAlaisVerifyHandle;

    private FileResultAlaisRepository fileResultAlaisRepository;

    /**
     * 导入Excel
     *
     * @param file      传入的文件
     * @param objectMap 业务数据
     * @param response  响应体
     * @return 导入的数据
     * @throws Exception 异常
     */
    @Override
    @Transactional
    public List<DtoFileResultAlais> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        //校验文件类型
        PoiExcelUtils.verifyFileType(file);
        //获取业务参数
        String planId = (String) objectMap.get(0);
        //获取导入校验后的数据
        ExcelImportResult<DtoImportFileResultAlais> excelData = getExcelData(file, response);
        List<DtoImportFileResultAlais> list = excelData.getList();
        if (StringUtil.isEmpty(list)) {
            throw new BaseException("文件中无数据或者上传文件模板错误，请检查后重新上传");
        }
        //清理线程变量
//        fixedPointVerifyHandle.getRelationTl().remove();
        //转换实体
        List<DtoFileResultAlais> resultAlaisList = importToEntity(list, planId);
        checkExist(resultAlaisList, planId);
        //保存数据
        addData(resultAlaisList);
        return resultAlaisList;
    }

    /**
     * 查重方法
     *
     * @param entityList 实体列表
     * @param planId     方案id
     */
    private void checkExist(List<DtoFileResultAlais> entityList, String planId) {
        List<DtoFileResultAlais> resultList = fileResultAlaisRepository.findByPlanIdAndIsDeletedFalse(planId);
        if (StringUtil.isNotEmpty(resultList)) {
            for (DtoFileResultAlais entity : entityList) {
                resultList = resultList.stream().filter(p -> p.getResultType().equals(entity.getResultType()) && p.getParamName().equals(entity.getParamName())
                        && p.getParamAlias().equals(entity.getParamAlias())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(resultList)) {
                    throw new BaseException("存在重复结果配置：" + entity.getParamName());
                }
            }

        }
    }

    /**
     * 保存数据
     *
     * @param data 需要导入的数据
     */
    @Override
    public void addData(List<DtoFileResultAlais> data) {
        if (StringUtil.isNotEmpty(data)) {
            fileResultAlaisRepository.save(data);
        }
    }

    /**
     * 获取Excel中的数据
     *
     * @param file     传入的文件
     * @param response 响应体
     * @return 导入的数据
     * @throws Exception 异常
     */
    @Override
    public ExcelImportResult<DtoImportFileResultAlais> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(resultAlaisVerifyHandle);
        ExcelImportResult<DtoImportFileResultAlais> result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoImportFileResultAlais.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "企业信息");
            PoiExcelUtils.downLoadExcel("企业导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }


    /**
     * 转换为需要保存的实体
     *
     * @param importList 导入的数据
     * @return 实体集合
     */
    private List<DtoFileResultAlais> importToEntity(List<DtoImportFileResultAlais> importList, String planId) {
        List<DtoFileResultAlais> resultAlaisList = new ArrayList<>();
        for (DtoImportFileResultAlais dto : importList) {
            DtoFileResultAlais resultAlais = new DtoFileResultAlais();
            //赋值属性
            BeanUtils.copyProperties(dto, resultAlais);
            resultAlais.setPlanId(planId);
            if (EnumResultType.NAME.getName().equals(dto.getResultType())) {
                resultAlais.setResultType(EnumResultType.NAME.getValue());
            } else if (EnumResultType.PARAM.getName().equals(dto.getResultType())) {
                resultAlais.setResultType(EnumResultType.PARAM.getValue());
            }
            //添加数据
            resultAlaisList.add(resultAlais);
        }
        return resultAlaisList;
    }

    @Autowired
    public void setFileResultAlaisRepository(FileResultAlaisRepository fileResultAlaisRepository) {
        this.fileResultAlaisRepository = fileResultAlaisRepository;
    }

    @Autowired
    public void setResultAlaisVerifyHandle(ResultAlaisVerifyHandle resultAlaisVerifyHandle) {
        this.resultAlaisVerifyHandle = resultAlaisVerifyHandle;
    }
}
