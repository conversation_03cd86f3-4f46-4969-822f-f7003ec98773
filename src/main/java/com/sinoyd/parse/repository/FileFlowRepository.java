package com.sinoyd.parse.repository;

import com.sinoyd.parse.dto.DtoFileFlow;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * FileFlow数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface FileFlowRepository extends IBaseJpaRepository<DtoFileFlow, String> {
    /**
     * 根据方案id查询流程配置
     * @param configIds 方案id列表
     * @return List<DtoFileFlow>
     */
    List<DtoFileFlow> findByPlanIdIn(List<String> configIds);

    /**
     * 同一方案下,流程名查重
     * @param flowName 流程名
     * @param planId 方案id
     * @param id 流程id
     * @return Integer
     */
    Integer countByFlowNameAndPlanIdAndIdNot(String flowName, String planId, String id);

    /**
     * 同方案流程步判重
     * @param stepNum 步骤
     * @param planId 方案id
     * @param id 流程id
     * @return Integer
     */
    Integer countByStepNumAndPlanIdAndIdNot(Integer stepNum, String planId, String id);

    /**
     * 根据方案id查询流程
     * @param planId 方案id
     * @return 流程
     */
    List<DtoFileFlow> findByPlanId(String planId);

    /**
     * 查询已删除的方案流程
     *
     * @param ids 流程id列表
     * @return 已删除的方案流程
     */
    @Query(value = "select * from TB_Parse_FileFlow f where f.id in :ids and f.isDeleted = 1", nativeQuery = true)
    List<DtoFileFlow> findDeleted(@Param("ids") List<String> ids);
}