package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoAiInstrumentConfig;

/**
 * AI仪器解析配置数据访问操作接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/07
 **/
public interface AiInstrumentConfigRepository extends IBaseJpaRepository<DtoAiInstrumentConfig, String> {

    /**
     * 根据仪器名称判重（排除指定ID）
     *
     * @param instrumentName 仪器名称
     * @param id 排除的ID
     * @return 数量
     */
    Integer countByInstrumentNameAndIdNot(String instrumentName, String id);

    /**
     * 根据仪器编号判重（排除指定ID）
     *
     * @param instrumentCode 仪器编号
     * @param id 排除的ID
     * @return 数量
     */
    Integer countByInstrumentCodeAndIdNot(String instrumentCode, String id);
}
