package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoStreamParamConfig;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * 解析流方案参数配置数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface StreamParamConfigRepository extends IBaseJpaRepository<DtoStreamParamConfig, String> {

    /**
     * 根据方案id,查找方案参数配置信息列表
     *
     * @param planId 方案id
     * @return List<DtoStreamParamConfig>
     */
    List<DtoStreamParamConfig> findByPlanId(String planId);

    /**
     * 根据方案参数配置名称,查找方案参数配置信息
     *
     * @param paramName 参数配置名称
     * @return List<DtoStreamParamConfig>
     */
    List<DtoStreamParamConfig> findByParamNameAndIsDeleted(String paramName, Boolean isDeleted);

    /**
     * 根据方案id及方案参数配置名称,查找方案参数配置信息
     *
     * @param paramName 参数配置名称
     * @return List<DtoStreamParamConfig>
     */
    List<DtoStreamParamConfig> findByPlanIdAndParamNameAndIsDeleted(String planId, String paramName, Boolean isDeleted);

    /**
     * 根据方案id删除方案参数配置信息
     *
     * @param planIds   方案id列表
     * @param isDeleted 是否删除标志
     */
    @Modifying
    @Query("update DtoStreamParamConfig a set a.isDeleted =:isDeleted where a.planId in :planIds")
    void logicDeleteByPlanId(@Param("planIds") List<String> planIds, @Param("isDeleted") boolean isDeleted);


}