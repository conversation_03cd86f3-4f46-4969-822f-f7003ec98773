package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoInstrumentConfig;

import java.util.List;


/**
 * 解析流仪器配置数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface InstrumentConfigRepository extends IBaseJpaRepository<DtoInstrumentConfig, String> {


    /**
     * 根据主键id查询仪器配置信息
     *
     * @param ids 主键列表
     * @return List<DtoInstrumentConfig>
     */
    List<DtoInstrumentConfig> findByIdIn(List<String> ids);
}