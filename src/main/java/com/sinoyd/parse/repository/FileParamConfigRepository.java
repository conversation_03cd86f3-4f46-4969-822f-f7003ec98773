package com.sinoyd.parse.repository;

import com.sinoyd.parse.dto.DtoFileParamConfig;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * FileParamConfig数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface FileParamConfigRepository extends IBaseJpaRepository<DtoFileParamConfig, String> {

    /**
     * 根据流程配置id删除参数配置
     * @param flowIds 流程配置id列表
     */
    @Transactional
    @Modifying
    @Query("update DtoFileParamConfig as a set a.isDeleted = 1,a.modifyDate=:modifyDate where a.flowId in:flowIds")
    void deleteByFlowIds(@Param("flowIds") List<String> flowIds, @Param("modifyDate") Date modifyDate);

    /**
     * 同一流程配置下,参数判重
     * @param paramName 参数名称
     * @param flowId 流程id
     * @param id 参数id
     * @return Integer
     */
    Integer countByParamNameAndFlowIdAndIdNot(String paramName, String flowId, String id);
}