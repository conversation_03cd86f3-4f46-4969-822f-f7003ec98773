package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoOrgStream;
import org.springframework.data.jpa.repository.Query;


/**
 * 源数据流访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface OrgStreamRepository extends IBaseJpaRepository<DtoOrgStream, String> {

    /**
     * 根据解析流日志主键id,查找源数据流信息
     *
     * @param id 解析流日志主键id
     * @return DtoOrgStream
     */
    @Query("select new com.sinoyd.parse.dto.DtoOrgStream(a.streamContent) from DtoOrgStream a where a.parseStreamLogId = ?1 and a.isDeleted = 0")
    DtoOrgStream findStreamContent(String id);
}