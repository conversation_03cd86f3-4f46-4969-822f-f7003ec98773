package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoErrorLog;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * ErrorLog数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface ErrorLogRepository extends IBaseJpaRepository<DtoErrorLog, String> {

    /**
     * 根据流程配置id删除错误日志
     * @param flowIds 流程配置id列表
     */
    @Transactional
    @Modifying
    @Query("update DtoErrorLog as a set a.isDeleted = 1,a.modifyDate=:modifyDate where a.flowId in:flowIds")
    void deleteByFlowIds(@Param("flowIds") List<String> flowIds, @Param("modifyDate") Date modifyDate);

    /**
     * 根据日志id查询错误日志
     * @param logId 日志id
     * @return DtoErrorLog
     */
    List<DtoErrorLog> findByParselogId(String logId);

    /**
     * 根据流程id查找错误记录
     * @param flowId 流程id
     * @return 错误日志
     */
    DtoErrorLog findByFlowId(String flowId);
}