package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoStreamApp;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * 解析流应用数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface StreamAppRepository extends IBaseJpaRepository<DtoStreamApp, String> {


    /**
     * 根据应用名称,查找解析流应用信息
     *
     * @param appName   解析应用名称
     * @param isDeleted 是否删除标志
     * @return List<DtoStreamApp>
     */
    List<DtoStreamApp> findByAppNameAndIsDeleted(String appName, Boolean isDeleted);

    /**
     * 根据应用名称列表,查找解析流应用信息
     *
     * @param appNames  解析应用名称列表
     * @param isDeleted 是否删除标志
     * @return List<DtoStreamApp>
     */
    List<DtoStreamApp> findByAppNameInAndIsDeleted(List<String> appNames, Boolean isDeleted);

    /**
     * 根据解析方案主键,删除对应的解析应用
     *
     * @param planIds   解析方案id列表
     * @param isDeleted 是否删除标志
     */
    @Modifying
    @Query("update DtoStreamApp a set a.isDeleted =:isDeleted where a.planId in :planIds")
    void logicDeleteByPlanId(@Param("planIds") List<String> planIds, @Param("isDeleted") boolean isDeleted);

    /**
     * 根据解析应用主键,查询解析应用
     *
     * @param ids       解析应用id列表
     * @param isDeleted 是否删除标志
     * @return List<DtoStreamApp>
     */
    List<DtoStreamApp> findByIdInAndIsDeleted(List<String> ids, Boolean isDeleted);

}