package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoFileApp;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * FileApp数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface FileAppRepository extends IBaseJpaRepository<DtoFileApp, String> {
    /**
     * 根据方案id查找应用配置
     * @param configIds 方案id列表
     * @return List<DtoFileApp>
     */
    List<DtoFileApp> findByPlanIdIn(List<String> configIds);

    /**
     * 根据应用配置名称判重
     * @param appName 应用配置名称
     * @param id 应用id
     * @return Integer
     */
    Integer countByAppNameAndIdNot(String appName, String id);

    /**
     * 根据应用配置名称及解析类型判重
     * @param appName 应用配置名称
     * @param parseType 解析类型
     * @param id 应用id
     * @return Integer
     */
    Integer countByAppNameAndParseTypeAndIdNot(String appName, String parseType, String id);

    /**
     * 查询已删除的应用配置
     *
     * @param ids id列表
     * @return 已删除的应用配置
     */
    @Query(value = "select * from TB_Parse_FileApp a where a.id in :ids and a.isDeleted = 1", nativeQuery = true)
    List<DtoFileApp> findDeleted(@Param("ids") List<String> ids);
}