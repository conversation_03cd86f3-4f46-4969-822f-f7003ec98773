package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoFileResultAlais;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * FileResultAlais数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface FileResultAlaisRepository extends IBaseJpaRepository<DtoFileResultAlais, String> {

    /**
     * 判重
     *
     * @param planId     方案id
     * @param paramName  结果原始名称
     * @param resultType 结果类型
     * @param paramAlias 结果映射名称
     * @param id         结果映射id
     * @return 计数
     */
    Integer countByPlanIdAndParamNameAndResultTypeAndParamAliasAndIdNot(String planId, String paramName, String resultType, String paramAlias, String id);

    /**
     * 根据解析方案id删除
     *
     * @param configIds 解析方案id列表
     */
    @Transactional
    @Modifying
    @Query("update DtoFileResultAlais as a set a.isDeleted = 1,a.modifyDate=:modifyDate where  a.planId in:configIds")
    void deleteByConfigIds(@Param("configIds") List<String> configIds, @Param("modifyDate") Date modifyDate);

    /**
     * 根据方案id和结果类型查询
     *
     * @param planId     方案id
     * @param resultType 结果类型
     * @param id         结果映射id
     * @return 查询结构
     */
    List<DtoFileResultAlais> findByPlanIdAndResultTypeAndIdNotAndIsDeletedFalse(String planId, String resultType, String id);

    /**
     * 根据方案id查询
     *
     * @param planId 方案id
     * @return 查询结构
     */
    List<DtoFileResultAlais> findByPlanIdAndIsDeletedFalse(String planId);

    /**
     * 根据方案id列表查询
     *
     * @param planIdList 方案id列表
     * @return 查询结构
     */
    List<DtoFileResultAlais> findByPlanIdInAndIsDeletedFalse(List<String> planIdList);

    /**
     * 查询已删除的数据
     *
     * @param ids id列表
     * @return 已删除的数据
     */
    @Query(value = "select * from TB_Parse_FileResultAlais r where r.id in :ids and r.isDeleted = 1", nativeQuery = true)
    List<DtoFileResultAlais> findDeleted(@Param("ids") List<String> ids);
}