package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoDatas;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * Datas数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface DatasRepository extends IBaseJpaRepository<DtoDatas, String> {

    /**
     * 根据应用id删除解析数据
     *
     * @param appIds 应用id列表
     */
    @Transactional
    @Modifying
    @Query("update DtoDatas as a set a.isDeleted = 1,a.modifyDate=:modifyDate where a.appId in:appIds")
    void deleteByAppIds(@Param("appIds") List<String> appIds, @Param("modifyDate") Date modifyDate);

    /**
     * 根据日志id查询相关数据
     *
     * @param logId 日志id
     * @return List<DtoDatas>
     */
    List<DtoDatas> findByParselogId(String logId);

    /**
     * 根据样品编号查询相关数据
     *
     * @param sampleCodes 样品编号集合
     * @return 查询结果
     */
    List<DtoDatas> findBySampleCodeIn(Collection<String> sampleCodes);
}