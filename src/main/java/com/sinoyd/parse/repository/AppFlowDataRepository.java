package com.sinoyd.parse.repository;

import com.sinoyd.parse.dto.DtoAppFlowData;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * AppFlowData数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface AppFlowDataRepository extends IBaseJpaRepository<DtoAppFlowData, String> {

    /**
     * 根据流程配置id删除应用流程数据
     * @param flowIds 流程配置id列表
     */
    @Transactional
    @Modifying
    @Query("update DtoAppFlowData as a set a.isDeleted = 1,a.modifyDate=:modifyDate where a.flowId in:flowIds")
    void deleteByFlowIds(@Param("flowIds") List<String> flowIds, @Param("modifyDate") Date modifyDate);

    /**
     * 根据日志id列表查询应用流程数据表
     * @param logIds 日志id列表
     * @return List<DtoAppFlowData>
     */
    List<DtoAppFlowData> findByParselogIdIn(List<String> logIds);

    /**
     * 根据日志id查询流程数据
     * @param logId 日志id
     * @return List<DtoAppFlowData>
     */
    List<DtoAppFlowData> findByParselogId(String logId);
}