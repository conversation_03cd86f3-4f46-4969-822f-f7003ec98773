package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoDatasTest;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * DatasTest数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface DatasTestRepository extends IBaseJpaRepository<DtoDatasTest, String> {

    /**
     * 根据应用id删除解析数据
     * @param appIds 应用id列表
     */
    @Transactional
    @Modifying
    @Query("update DtoDatasTest as a set a.isDeleted = 1,a.modifyDate=:modifyDate where a.appId in:appIds")
    void deleteByAppIds(@Param("appIds") List<String> appIds, @Param("modifyDate") Date modifyDate);

    /**
     * 根据解析日志id查询
     * @param parselogId 解析日志id
     * @return List<DtoDatasTest>
     */
    List<DtoDatasTest> findByParselogId(String parselogId);
}