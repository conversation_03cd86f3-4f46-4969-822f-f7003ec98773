package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoStreamConfig;

import java.util.List;


/**
 * 解析流方案配置数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface StreamConfigRepository extends IBaseJpaRepository<DtoStreamConfig, String> {

    /**
     * 根据方案名,查找解析流方案配置信息
     *
     * @param planName  方案名
     * @param isDeleted 是否删除标志
     * @return DtoStreamConfig
     */
    List<DtoStreamConfig> findByPlanNameAndIsDeleted(String planName, Boolean isDeleted);
}