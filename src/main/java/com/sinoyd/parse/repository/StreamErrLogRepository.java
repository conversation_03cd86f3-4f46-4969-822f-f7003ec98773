package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoStreamErrLog;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


/**
 * 解析流错误日志数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface StreamErrLogRepository extends IBaseJpaRepository<DtoStreamErrLog, String> {

    /**
     * 根据解析流日志id 查询解析流错误日志信息列表
     *
     * @param id 解析流日志id
     * @return List<DtoStreamErrLog>
     */
    @Query("select new com.sinoyd.parse.dto.DtoStreamErrLog(a.id, a.errorType, a.logContent, a.errorTime) from DtoStreamErrLog a where a.parseStreamLogId = ?1 " +
            "and a.isDeleted = 0 order by a.errorTime")
    List<DtoStreamErrLog> findByParseStreamId(String id);

    /**
     * 根据解析流错误日志id 查询解析流错误日志详细信息
     *
     * @param id 解析流错误日志id
     * @return List<DtoStreamErrLog>
     */
    @Query("select new com.sinoyd.parse.dto.DtoStreamErrLog(a.errorType, a.errorTime, a.logContent) from DtoStreamErrLog a where a.id = ?1 and a.isDeleted = 0")
    DtoStreamErrLog findErrLogDetail(String id);
}