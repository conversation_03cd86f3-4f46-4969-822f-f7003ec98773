package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoParseDocument;

import java.util.Collection;
import java.util.List;

/**
 * 仪器解析文件管理数据访问操作接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
public interface ParseDocumentRepository extends IBaseJpaRepository<DtoParseDocument, String> {

    /**
     * 根据对象ID查询文档列表
     *
     * @param objectId 对象ID
     * @return 文档列表
     */
    List<DtoParseDocument> findByObjectId(String objectId);

    /**
     * 根据对象ID和文件类型查询文档列表
     *
     * @param objectId  对象ID
     * @param docTypeId 文件类型
     * @return 文档列表
     */
    List<DtoParseDocument> findByObjectIdAndDocTypeId(String objectId, String docTypeId);

    /**
     * 根据对象ID列表查询文档列表
     *
     * @param objectIds 对象ID列表
     * @param docTypeId 文件类型
     * @return 文档列表
     */
    List<DtoParseDocument> findByObjectIdInAndDocTypeId(Collection<String> objectIds, String docTypeId);
}
