package com.sinoyd.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.parse.dto.DtoFileConfig;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * FileConfig数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface FileConfigRepository extends IBaseJpaRepository<DtoFileConfig, String> {

    /**
     * 根据方案名,处理方式,方案id查重
     * @param planName 方案名
     * @param handleType 处理方式
     * @param id 方案id
     * @return Integer
     */
    Integer countByPlanNameAndHandleTypeAndIdNot(String planName, String handleType, String id);

    /**
     * 查询已删除的方案
     *
     * @param ids 方案id列表
     * @return 已删除的方案
     */
    @Query(value = "select * from TB_Parse_FileConfig c where c.id in :ids and c.isDeleted = 1", nativeQuery = true)
    List<DtoFileConfig> findDeleted(@Param("ids") List<String> ids);
}