import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.parse.criteria.StreamAppCriteria;
import com.sinoyd.parse.dto.*;
import com.sinoyd.parse.repository.InstrumentConfigRepository;
import com.sinoyd.parse.repository.StreamAppRepository;
import com.sinoyd.parse.repository.StreamConfigRepository;
import com.sinoyd.parse.service.impl.StreamAppServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StreamAppServiceImplTest {

    // @InjectMock 注入被测对象, 一般写在最前面
    @InjectMocks
    StreamAppServiceImpl streamAppServiceImpl;

    @Mock
    private StreamAppRepository repository;

    @Mock
    private StreamConfigRepository streamConfigRepository;

    @Mock
    private InstrumentConfigRepository instrumentConfigRepository;

    @Mock
    private CommonRepository commonRepository;

    @Mock
    private CommonRepository comRepository;


    // 执行测试之前执行
    @Before
    public void setup() {
        // 初始化 mock 注入环境
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testFindByPage() {

        // 断言：streamAppServiceImpl 对象不为 null
        Assert.assertNotNull(streamAppServiceImpl);

        PageBean<DtoStreamApp> pageBean = new PageBean<>();
        StreamAppCriteria streamAppCriteria = new StreamAppCriteria();
        streamAppCriteria.setAppInstConfigName("appName");

        doAnswer(invocation -> {
            PageBean<DtoStreamApp> arg2 = invocation.getArgumentAt(0, PageBean.class);
            arg2.setData(new ArrayList<>());
            return null;
        }).when(comRepository).findByPage(pageBean, streamAppCriteria);

        streamAppServiceImpl.findByPage(pageBean, streamAppCriteria);

        Assert.assertNotNull(pageBean);

    }

    @Test
    public void testFindStreamApp() {
        // 断言：streamAppServiceImpl 对象不为 null
        Assert.assertNotNull(streamAppServiceImpl);
        String id = "52d26d5c-b1fc-4c44-86e2-c348d05785f1";
        DtoStreamApp dtoStreamApp = new DtoStreamApp(id, null, null, null, null);
        dtoStreamApp.setInstrumentConfigId("9fbbb3a6-6e5d-4fba-ac4a-011403a0aa32");
        dtoStreamApp.setPlanId("66625125-e58d-46ae-a7e8-8c6c40bcfca0");
        String instConfigId = dtoStreamApp.getInstrumentConfigId();
        DtoInstrumentConfig instConfig = new DtoInstrumentConfig();
        DtoStreamConfig streamConfig = new DtoStreamConfig();
        String planId = dtoStreamApp.getPlanId();

        when(repository.findOne(id)).thenReturn(dtoStreamApp);
        when(instrumentConfigRepository.findOne(instConfigId)).thenReturn(instConfig);
        when(streamConfigRepository.findOne(planId)).thenReturn(streamConfig);

        DtoStreamAppInstrumentConfig dtoStreamAppInstrumentConfig = streamAppServiceImpl.findStreamApp(id);
        // 断言：判断成功
        Assert.assertEquals("streamConfig not match", "66625125-e58d-46ae-a7e8-8c6c40bcfca0", dtoStreamAppInstrumentConfig.getPlanId());
    }

    @Test
    public void testSaveStreamApp() {

        // 断言：streamAppServiceImpl 对象不为 null
        Assert.assertNotNull(streamAppServiceImpl);
        DtoStreamAppInstrumentConfig dtoStrAppInstConfig = new DtoStreamAppInstrumentConfig("测试应用", "d8cc2e00-f406-4500-95f9-564397a98b8c");
        dtoStrAppInstConfig.setAppName("测试应用");
        String planId = dtoStrAppInstConfig.getPlanId();
        String appName = dtoStrAppInstConfig.getAppName();
        DtoStreamConfig dtoStreamConfig = new DtoStreamConfig();
        List<DtoStreamApp> oriDtoStreamAppList = new ArrayList<>();

        when(streamConfigRepository.findOne(planId)).thenReturn(dtoStreamConfig);
        when(repository.findByAppNameAndIsDeleted(appName, false)).thenReturn(oriDtoStreamAppList);

        DtoStreamAppInstrumentConfig data = streamAppServiceImpl.saveStreamApp(dtoStrAppInstConfig);
        // 断言：判断成功
        Assert.assertEquals("streamConfig not match", "测试应用", data.getAppName());
    }

    @Test
    public void testCopyStreamApp() {

        // 断言：streamAppServiceImpl 对象不为 null
        List<String> ids = new ArrayList<>();
        ids.add("52d26d5c-b1fc-4c44-86e2-c348d05785f1");
        ids.add("28ab9e52-63d7-400d-9aa2-1b87f284aced");
        List<DtoStreamApp> dtoStreamApps = new ArrayList<>();
        DtoStreamApp dtoStreamApp1 = new DtoStreamApp("52d26d5c-b1fc-4c44-86e2-c348d05785f1","测试应用1",null,null,null);
        dtoStreamApp1.setInstrumentConfigId("9fbbb3a6-6e5d-4fba-ac4a-011403a0aa32");
        DtoStreamApp dtoStreamApp2 = new DtoStreamApp("28ab9e52-63d7-400d-9aa2-1b87f284aced","测试应用2",null,null,null);
        dtoStreamApp2.setInstrumentConfigId("75f3ba66-2778-4d56-8f53-d42d11fa3c8b");

        dtoStreamApps.add(dtoStreamApp1);
        dtoStreamApps.add(dtoStreamApp2);
        List<DtoStreamApp> validateDtoStreamAppList = new ArrayList<>();
        List<String> validateAppNames = new ArrayList<>();
        validateAppNames.add("测试应用1复制");
        validateAppNames.add("测试应用2复制");
        List<String> instConfigIds = new ArrayList<>();
        instConfigIds.add(dtoStreamApp1.getInstrumentConfigId());
        instConfigIds.add(dtoStreamApp2.getInstrumentConfigId());
        List<DtoInstrumentConfig> dtoInstrumentConfigs = new ArrayList<>();
        DtoInstrumentConfig instrumentConfig1 = new DtoInstrumentConfig();
        instrumentConfig1.setId(instConfigIds.get(0));
        DtoInstrumentConfig instrumentConfig2 = new DtoInstrumentConfig();
        instrumentConfig2.setId(instConfigIds.get(1));
        dtoInstrumentConfigs.add(instrumentConfig1);
        dtoInstrumentConfigs.add(instrumentConfig2);

        when(repository.findByAppNameInAndIsDeleted(validateAppNames, false)).thenReturn(validateDtoStreamAppList);
        when(repository.findByIdInAndIsDeleted(ids, false)).thenReturn(dtoStreamApps);
        when(instrumentConfigRepository.findByIdIn(instConfigIds)).thenReturn(dtoInstrumentConfigs);

        List<DtoStreamApp> streamAppList = streamAppServiceImpl.copyStreamApp(ids);
        Assert.assertTrue(streamAppList.isEmpty());
    }
//
//
    @Test
    public void testUpdateStreamApp() {

        // 断言：streamAppServiceImpl 对象不为 null
        Assert.assertNotNull(streamAppServiceImpl);
        DtoStreamAppInstrumentConfig entity = new DtoStreamAppInstrumentConfig("修改应用", "66625125-e58d-46ae-a7e8-8c6c40bcfca0");
        entity.setAppId("52d26d5c-b1fc-4c44-86e2-c348d05785f1");
        DtoStreamApp oriDtoStreamApp = new DtoStreamApp("52d26d5c-b1fc-4c44-86e2-c348d05785f1", "原应用", null,null,null);
        oriDtoStreamApp.setPlanId(entity.getPlanId());
        oriDtoStreamApp.setIsDeleted(false);
        List<DtoStreamApp> dtoStreamAppList = new ArrayList<>();
        DtoInstrumentConfig oriInstConfig = new DtoInstrumentConfig();


        when(repository.findOne(entity.getAppId())).thenReturn(oriDtoStreamApp);
        when(repository.findByAppNameAndIsDeleted(entity.getAppName(), false)).thenReturn(dtoStreamAppList);
        when(instrumentConfigRepository.findOne(oriDtoStreamApp.getInstrumentConfigId())).thenReturn(oriInstConfig);

        DtoStreamAppInstrumentConfig data = streamAppServiceImpl.updateStreamApp(entity);
        // 断言：判断成功
        Assert.assertEquals("streamConfig not match","修改应用",data.getAppName());
    }

    @Test
    public void testdeleteByIds() {

        // 断言：streamAppServiceImpl 对象不为 null
        Assert.assertNotNull(streamAppServiceImpl);
        List<String> ids = new ArrayList<>();
        ids.add("52d26d5c-b1fc-4c44-86e2-c348d05785f1");
        DtoStreamApp dtoStreamApp = new DtoStreamApp();
        dtoStreamApp.setInstrumentConfigId("9fbbb3a6-6e5d-4fba-ac4a-011403a0aa32");
        List<DtoStreamApp> dtoStreamApps = new ArrayList<>();
        dtoStreamApps.add(dtoStreamApp);

        when(repository.findByIdInAndIsDeleted(ids, false)).thenReturn(dtoStreamApps);

        int cnt = streamAppServiceImpl.deleteByIds(ids);
        // 断言：判断成功
        Assert.assertTrue("streamConfig not match",cnt == 0);
    }

    @Test
    public void testFindMobileStreamApp() {

        // 断言：streamConfigServiceImpl 对象不为 null
        Assert.assertNotNull(streamAppServiceImpl);
        List<DtoStreamAppMobileSync> dtoStreamAppMobileSyncList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        when(commonRepository.find(sb.toString())).thenReturn(dtoStreamAppMobileSyncList);

        List<DtoStreamAppMobileSync> data = streamAppServiceImpl.findMobileStreamApp();
        // 断言：判断成功
        Assert.assertTrue("streamConfig not match",data.isEmpty());
    }

}
