FROM registry.cn-shanghai.aliyuncs.com/dn/java

ADD ./bin /app

WORKDIR /app
RUN sh ./initlibs.sh
RUN rm -r ./libs

ENV JAVA_OPTS=""
ENTRYPOINT ["java", "-server", "-Duser.timezone=GMT+08", "-Xms128m","-Xmx256m","-XX:CompressedClassSpaceSize=64m","-XX:MetaspaceSize=64m","-XX:MaxMetaspaceSize=128m","-Djava.security.egd=file:/dev/./urandom", "-Dloader.path=lib/", "-Dfile.encoding=utf-8","-jar","/app/app.jar", "--spring.profiles.active=prod"]

# Service listens on port 80
EXPOSE 80
